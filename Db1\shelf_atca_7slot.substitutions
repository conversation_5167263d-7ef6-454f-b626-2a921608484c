#==============================================================================
#
# Abs:  Populate ATCA shelf PVs
#
# Name:  shelf_atca_7slot.substitutions
#
# Macros in:
#	crat    Shelf name, for example CRAT:LI28:RF01
#       node    Name of shelf used in asynIPPortConfigure in st.cmd, for example crat-li28-rf01
#	id      String identifier, for example 'CU' for cooling unit
#       unit    Instance of this type of module, for example 2 for the second MCH in a shelf
#	loca	Building, rack, elevation
#
#
# Macros out:
#
#       dev     Shelf Name, for example CRAT:LI28:RF01
#       link    Name of shelf used in asynIPPortConfigure in st.cmd, for example crat-li28-rf01
#	location Building, rack, elevation
#
#
#==============================================================================
#

file system_common.db
{
   pattern { dev	, link		, location	}
	   { $(dev)	, $(link)	, $(location)	}	
}

file system_chassis_status.db
{
   pattern { dev	}
	   { $(dev)	}	
}

file fru_basic.db
{
   pattern { dev	, id	, unit	, fruid	}
           { $(dev)	, SHM	, 1  	, 254	}
           { $(dev)	, SHM	, 2  	, 253	}
}

file fru_atca_fb.db
{
   pattern { dev	, id	, unit	, fruid	}
           { $(dev)	, FB	, 1  	, 5	}
           { $(dev)	, FB	, 2  	, 6	}
           { $(dev)	, FB	, 3  	, 7	}
           { $(dev)	, FB	, 4  	, 8	}
           { $(dev)	, FB	, 5  	, 9	}
           { $(dev)	, FB	, 6  	, 10	}
           { $(dev)	, FB	, 7  	, 11	}
#           { $(dev)	, FB	, 8  	, 12	}
#           { $(dev)	, FB	, 9  	, 13	}
#           { $(dev)	, FB	, 10  	, 14	}
#           { $(dev)	, FB	, 11  	, 15	}
#           { $(dev)	, FB	, 12  	, 16	}
}

file fru_atca_rtm.db
{
   pattern { dev	, id	, unit	, fruid	}
           { $(dev)	, RTM	, 1  	, 90	}
           { $(dev)	, RTM	, 2  	, 91	}
           { $(dev)	, RTM	, 3  	, 92	}
           { $(dev)	, RTM	, 4  	, 93	}
           { $(dev)	, RTM	, 5  	, 94	}
           { $(dev)	, RTM	, 6  	, 95	}
           { $(dev)	, RTM	, 7  	, 96	}
#           { $(dev)	, RTM	, 8  	, 97	}
#           { $(dev)	, RTM	, 9  	, 98	}
#           { $(dev)	, RTM	, 10  	, 99	}
#           { $(dev)	, RTM	, 11 	, 100	}
#           { $(dev)	, RTM	, 12  	, 101	}
}

file fru_cu.db
{
   pattern { dev	, id	, unit	, fruid	}
           { $(dev)	, CU	, 1  	, 40	}
           { $(dev)	, CU	, 2  	, 41	}
           { $(dev)	, CU	, 3  	, 42	}
           { $(dev)	, CU	, 4  	, 43	}
}

file fru_common.db
{
   pattern { dev	, id	, unit	, fruid }
           { $(dev)	, PM	, 1    	, 50	}
           { $(dev)	, PM	, 2  	, 51	}
           { $(dev)	, PM	, 3  	, 52	}
           { $(dev)	, PM	, 4  	, 53	}
           { $(dev)	, SH	, 1  	, 1	}
           { $(dev)	, SH	, 2  	, 2	}
}

