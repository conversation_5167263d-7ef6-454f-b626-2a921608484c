#==============================================================================
#
# Abs:  Populate LCLS Supermicro and Advantech server PVs
#
# Name:  server_pc_lcls.substitutions
#
# Macros in:
#	dev     Shelf name, for example CRAT:LI28:RF01
#       link    Name of shelf used in asynIPPortConfigure in st.cmd, for example crat-li28-rf01
#	location Building, rack, elevation
#       inpa - inpl PVs to include in alarm summary (.SEVR field of specified PV is used)
#	prefix   String in PV name that identifies the FRU sensor is associated with (if there is one)
#		 PV name syntax: $(dev):$(prefix)$(attr)$(sensinst)
#		 For microTCA systems, prefix is "<frutype><instance>_", for example AMC1_ for the first AMC
#		 or CU2_ for the second cooling unit
#		 For supermicro, the sensors are not associated with a FRU, so prefix is an empty string
#	aliasprefix Temporary string to keep PV names backward-compatible via alias
#	id       String identifier, for example 'CU' for cooling unit
#       unit     Instance of this type of module, for example 2 for the second MCH in a carrier
#       attr	 Used in PV attribute, for example FAN1SPEED or TEMP2
#       sensinst Instance of this type of sensor on this module
#       type     Sensor type code, defined in IPMI spec (and src/ipmiDef.h)
#       fruid    FRU ID according to MicroTCA spec
#
#==============================================================================
#

file server_pc.db
{
   pattern { dev	, link		, location	}
	   { $(dev)	, $(link)	, $(location)	}	
}

file system_common_lcls.db
{
   pattern { dev	, link		, location	}
	   { $(dev)	, $(link)	, $(location)	}	
}