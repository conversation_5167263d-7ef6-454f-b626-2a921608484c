#!../../bin/linux-x86_64/ipmiCommIoc

#==============================================================================
# 自定义MicroTCA监控启动脚本
# 使用精简的数据库文件 shelf_microtca_custom.db
# 只包含实际存在的148个传感器，避免无效PV
#==============================================================================

# 设置环境变量
< envPaths
epicsEnvSet("EPICS_CA_SERVER_PORT", "10022")

# 切换到应用顶层目录
cd ../../

# 加载数据库定义
dbLoadDatabase("dbd/ipmiCommIoc.dbd")
ipmiCommIoc_registerRecordDeviceDriver(pdbbase)

#==============================================================================
# MicroTCA 机箱配置
#==============================================================================

# 配置异步IP端口 - 连接到您的MicroTCA MCH
drvAsynIPPortConfigure("microtca-test", "************:623 udp", 0, 0, 0)

# 初始化MCH设备
mchInit("microtca-test")

# 加载自定义数据库文件 - 只包含实际存在的传感器
dbLoadRecords("db/shelf_microtca_custom.db", "dev=CRAT:MICROTCA:01,link=microtca-test,location=Lab-Rack1")

#==============================================================================
# 可选调试设置
#==============================================================================

# 启用调试信息 (可选)
# var mchDebug 1

# 启用Asyn调试 (可选)
# asynSetTraceMask("microtca-test", 0, 0x09)
# asynSetTraceIOMask("microtca-test", 0, 0x02)

#==============================================================================
# IOC 初始化
#==============================================================================

# 启动IOC
iocInit()

#==============================================================================
# 启动后信息显示
#==============================================================================

echo ""
echo "=============================================================================="
echo "自定义MicroTCA监控系统启动完成!"
echo "=============================================================================="
echo ""
echo "系统信息:"
echo "  设备前缀: CRAT:MICROTCA:01"
echo "  MCH地址:  ************:623"
echo "  位置:     Lab-Rack1"
echo ""
echo "主要传感器组:"
echo "  系统状态: CRAT:MICROTCA:01:ONLNSTAT, INIT, CONNECT"
echo "  AMC1:     CRAT:MICROTCA:01:AMC1:*"
echo "  CU1/CU2:  CRAT:MICROTCA:01:CU1:*, CRAT:MICROTCA:01:CU2:*"
echo "  PM2:      CRAT:MICROTCA:01:PM2:*"
echo "  MCH:      CRAT:MICROTCA:01:MCH:*"
echo ""
echo "常用命令:"
echo "  dbl                                    - 列出所有 PV"
echo "  caget CRAT:MICROTCA:01:ONLNSTAT       - 检查MCH在线状态"
echo "  caget CRAT:MICROTCA:01:INIT           - 检查初始化状态"
echo "  camonitor CRAT:MICROTCA:01:AMC1:TEMP* - 监控AMC1温度"
echo "  camonitor CRAT:MICROTCA:01:CU1:FAN*   - 监控CU1风扇"
echo "  caput CRAT:MICROTCA:01:DBG 1          - 启用调试信息"
echo ""
echo "传感器统计:"
echo "  总传感器数: ~148 (基于实际硬件)"
echo "  相比原版:   从7457个PV减少到~200个PV"
echo ""
echo "=============================================================================="
