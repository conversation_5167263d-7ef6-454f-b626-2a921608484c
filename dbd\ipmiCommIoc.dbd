menu(serialPRTY) {
    choice(serialPRTY_unknown, "Unknown")
    choice(serialPRTY_None, "None")
    choice(serialPRTY_Even, "Even")
    choice(serialPRTY_Odd, "Odd")
}
menu(waveformPOST) {
    choice(waveformPOST_Always, "Always")
    choice(waveformPOST_OnChange, "On Change")
}
menu(aaoPOST) {
    choice(aaoPOST_Always, "Always")
    choice(aaoPOST_OnChange, "On Change")
}
menu(menuPriority) {
    choice(menuPriorityLOW, "LOW")
    choice(menuPriorityMEDIUM, "MEDIUM")
    choice(menuPriorityHIGH, "HIGH")
}
menu(serialSBIT) {
    choice(serialSBIT_unknown, "Unknown")
    choice(serialSBIT_1, "1")
    choice(serialSBIT_2, "2")
}
menu(calcoutDOPT) {
    choice(calcoutDOPT_Use_VAL, "Use CALC")
    choice(calcoutDOPT_Use_OVAL, "Use OCAL")
}
menu(menuOmsl) {
    choice(menuOmslsupervisory, "supervisory")
    choice(menuOmslclosed_loop, "closed_loop")
}
menu(menuFtype) {
    choice(menuFtypeSTRING, "STRING")
    choice(menuFtypeCHAR, "CHAR")
    choice(menuFtypeUCHAR, "UCHAR")
    choice(menuFtypeSHORT, "SHORT")
    choice(menuFtypeUSHORT, "USHORT")
    choice(menuFtypeLONG, "LONG")
    choice(menuFtypeULONG, "ULONG")
    choice(menuFtypeINT64, "INT64")
    choice(menuFtypeUINT64, "UINT64")
    choice(menuFtypeFLOAT, "FLOAT")
    choice(menuFtypeDOUBLE, "DOUBLE")
    choice(menuFtypeENUM, "ENUM")
}
menu(stringinPOST) {
    choice(stringinPOST_OnChange, "On Change")
    choice(stringinPOST_Always, "Always")
}
menu(menuPini) {
    choice(menuPiniNO, "NO")
    choice(menuPiniYES, "YES")
    choice(menuPiniRUN, "RUN")
    choice(menuPiniRUNNING, "RUNNING")
    choice(menuPiniPAUSE, "PAUSE")
    choice(menuPiniPAUSED, "PAUSED")
}
menu(dfanoutSELM) {
    choice(dfanoutSELM_All, "All")
    choice(dfanoutSELM_Specified, "Specified")
    choice(dfanoutSELM_Mask, "Mask")
}
menu(menuScan) {
    choice(menuScanPassive, "Passive")
    choice(menuScanEvent, "Event")
    choice(menuScanI_O_Intr, "I/O Intr")
    choice(menuScan10_second, "10 second")
    choice(menuScan5_second, "5 second")
    choice(menuScan2_second, "2 second")
    choice(menuScan1_second, "1 second")
    choice(menuScan_5_second, ".5 second")
    choice(menuScan_2_second, ".2 second")
    choice(menuScan_1_second, ".1 second")
}
menu(gpibACMD) {
    choice(gpibACMD_None, "None")
    choice(gpibACMD_Group_Execute_Trig___GET_, "Group Execute Trig. (GET)")
    choice(gpibACMD_Go_To_Local__GTL_, "Go To Local (GTL)")
    choice(gpibACMD_Selected_Dev__Clear__SDC_, "Selected Dev. Clear (SDC)")
    choice(gpibACMD_Take_Control__TCT_, "Take Control (TCT)")
    choice(gpibACMD_Serial_Poll, "Serial Poll")
}
menu(aSubLFLG) {
    choice(aSubLFLG_IGNORE, "IGNORE")
    choice(aSubLFLG_READ, "READ")
}
menu(asynTMOD) {
    choice(asynTMOD_Write_Read, "Write/Read")
    choice(asynTMOD_Write, "Write")
    choice(asynTMOD_Read, "Read")
    choice(asynTMOD_Flush, "Flush")
    choice(asynTMOD_NoIO, "NoI/O")
}
menu(ipDRTO) {
    choice(ipDRTO_unknown, "Unknown")
    choice(ipDRTO_No, "No")
    choice(ipDRTO_Yes, "Yes")
}
menu(menuPost) {
    choice(menuPost_OnChange, "On Change")
    choice(menuPost_Always, "Always")
}
menu(asynINTERFACE) {
    choice(asynINTERFACE_OCTET, "asynOctet")
    choice(asynINTERFACE_INT32, "asynInt32")
    choice(asynINTERFACE_UINT32, "asynUInt32Digital")
    choice(asynINTERFACE_FLOAT64, "asynFloat64")
}
menu(menuAlarmStat) {
    choice(menuAlarmStatNO_ALARM, "NO_ALARM")
    choice(menuAlarmStatREAD, "READ")
    choice(menuAlarmStatWRITE, "WRITE")
    choice(menuAlarmStatHIHI, "HIHI")
    choice(menuAlarmStatHIGH, "HIGH")
    choice(menuAlarmStatLOLO, "LOLO")
    choice(menuAlarmStatLOW, "LOW")
    choice(menuAlarmStatSTATE, "STATE")
    choice(menuAlarmStatCOS, "COS")
    choice(menuAlarmStatCOMM, "COMM")
    choice(menuAlarmStatTIMEOUT, "TIMEOUT")
    choice(menuAlarmStatHWLIMIT, "HWLIMIT")
    choice(menuAlarmStatCALC, "CALC")
    choice(menuAlarmStatSCAN, "SCAN")
    choice(menuAlarmStatLINK, "LINK")
    choice(menuAlarmStatSOFT, "SOFT")
    choice(menuAlarmStatBAD_SUB, "BAD_SUB")
    choice(menuAlarmStatUDF, "UDF")
    choice(menuAlarmStatDISABLE, "DISABLE")
    choice(menuAlarmStatSIMM, "SIMM")
    choice(menuAlarmStatREAD_ACCESS, "READ_ACCESS")
    choice(menuAlarmStatWRITE_ACCESS, "WRITE_ACCESS")
}
menu(aoOIF) {
    choice(aoOIF_Full, "Full")
    choice(aoOIF_Incremental, "Incremental")
}
menu(bufferingALG) {
    choice(bufferingALG_FIFO, "FIFO Buffer")
    choice(bufferingALG_LIFO, "LIFO Buffer")
}
menu(aaiPOST) {
    choice(aaiPOST_Always, "Always")
    choice(aaiPOST_OnChange, "On Change")
}
menu(calcoutINAV) {
    choice(calcoutINAV_EXT_NC, "Ext PV NC")
    choice(calcoutINAV_EXT, "Ext PV OK")
    choice(calcoutINAV_LOC, "Local PV")
    choice(calcoutINAV_CON, "Constant")
}
menu(asynAUTOCONNECT) {
    choice(asynAUTOCONNECT_noAutoConnect, "noAutoConnect")
    choice(asynAUTOCONNECT_autoConnect, "autoConnect")
}
menu(asynFMT) {
    choice(asynFMT_ASCII, "ASCII")
    choice(asynFMT_Hybrid, "Hybrid")
    choice(asynFMT_Binary, "Binary")
}
menu(seqSELM) {
    choice(seqSELM_All, "All")
    choice(seqSELM_Specified, "Specified")
    choice(seqSELM_Mask, "Mask")
}
menu(asynCONNECT) {
    choice(asynCONNECT_Disconnect, "Disconnect")
    choice(asynCONNECT_Connect, "Connect")
}
menu(gpibUCMD) {
    choice(gpibUCMD_None, "None")
    choice(gpibUCMD_Device_Clear__DCL_, "Device Clear (DCL)")
    choice(gpibUCMD_Local_Lockout__LL0_, "Local Lockout (LL0)")
    choice(gpibUCMD_Serial_Poll_Disable__SPD_, "Serial Poll Disable (SPD)")
    choice(gpibUCMD_Serial_Poll_Enable__SPE_, "Serial Poll Enable (SPE)")
    choice(gpibUCMD_Unlisten__UNL_, "Unlisten (UNL)")
    choice(gpibUCMD_Untalk__UNT_, "Untalk (UNT)")
}
menu(serialBAUD) {
    choice(serialBAUD_unknown, "Unknown")
    choice(serialBAUD_300, "300")
    choice(serialBAUD_600, "600")
    choice(serialBAUD_1200, "1200")
    choice(serialBAUD_2400, "2400")
    choice(serialBAUD_4800, "4800")
    choice(serialBAUD_9600, "9600")
    choice(serialBAUD_19200, "19200")
    choice(serialBAUD_38400, "38400")
    choice(serialBAUD_57600, "57600")
    choice(serialBAUD_115200, "115200")
    choice(serialBAUD_230400, "230400")
    choice(serialBAUD_460800, "460800")
    choice(serialBAUD_576000, "576000")
    choice(serialBAUD_921600, "921600")
    choice(serialBAUD_1152000, "1152000")
}
menu(histogramCMD) {
    choice(histogramCMD_Read, "Read")
    choice(histogramCMD_Clear, "Clear")
    choice(histogramCMD_Start, "Start")
    choice(histogramCMD_Stop, "Stop")
}
menu(asynTRACE) {
    choice(asynTRACE_Off, "Off")
    choice(asynTRACE_On, "On")
}
menu(asynEOMREASON) {
    choice(asynEOMREASONNone, "None")
    choice(asynEOMREASONCNT, "Count")
    choice(asynEOMREASONEOS, "Eos")
    choice(asynEOMREASONCNTEOS, "Count Eos")
    choice(asynEOMREASONEND, "End")
    choice(asynEOMREASONCNTEND, "Count End")
    choice(asynEOMREASONEOSEND, "Eos End")
    choice(asynEOMREASONCNTEOSEND, "Count Eos End")
}
menu(menuIvoa) {
    choice(menuIvoaContinue_normally, "Continue normally")
    choice(menuIvoaDon_t_drive_outputs, "Don't drive outputs")
    choice(menuIvoaSet_output_to_IVOV, "Set output to IVOV")
}
menu(stringoutPOST) {
    choice(stringoutPOST_OnChange, "On Change")
    choice(stringoutPOST_Always, "Always")
}
menu(menuAlarmSevr) {
    choice(menuAlarmSevrNO_ALARM, "NO_ALARM")
    choice(menuAlarmSevrMINOR, "MINOR")
    choice(menuAlarmSevrMAJOR, "MAJOR")
    choice(menuAlarmSevrINVALID, "INVALID")
}
menu(serialMCTL) {
    choice(serialMCTL_unknown, "Unknown")
    choice(serialMCTL_CLOCAL, "CLOCAL")
    choice(serialMCTL_Yes, "YES")
}
menu(serialFCTL) {
    choice(serialFCTL_unknown, "Unknown")
    choice(serialFCTL_None, "None")
    choice(serialFCTL_Hardware, "Hardware")
}
menu(menuSimm) {
    choice(menuSimmNO, "NO")
    choice(menuSimmYES, "YES")
    choice(menuSimmRAW, "RAW")
}
menu(compressALG) {
    choice(compressALG_N_to_1_Low_Value, "N to 1 Low Value")
    choice(compressALG_N_to_1_High_Value, "N to 1 High Value")
    choice(compressALG_N_to_1_Average, "N to 1 Average")
    choice(compressALG_Average, "Average")
    choice(compressALG_Circular_Buffer, "Circular Buffer")
    choice(compressALG_N_to_1_Median, "N to 1 Median")
}
menu(aSubEFLG) {
    choice(aSubEFLG_NEVER, "NEVER")
    choice(aSubEFLG_ON_CHANGE, "ON CHANGE")
    choice(aSubEFLG_ALWAYS, "ALWAYS")
}
menu(fanoutSELM) {
    choice(fanoutSELM_All, "All")
    choice(fanoutSELM_Specified, "Specified")
    choice(fanoutSELM_Mask, "Mask")
}
menu(calcoutOOPT) {
    choice(calcoutOOPT_Every_Time, "Every Time")
    choice(calcoutOOPT_On_Change, "On Change")
    choice(calcoutOOPT_When_Zero, "When Zero")
    choice(calcoutOOPT_When_Non_zero, "When Non-zero")
    choice(calcoutOOPT_Transition_To_Zero, "Transition To Zero")
    choice(calcoutOOPT_Transition_To_Non_zero, "Transition To Non-zero")
}
menu(asynENABLE) {
    choice(asynENABLE_Disable, "Disable")
    choice(asynENABLE_Enable, "Enable")
}
menu(menuConvert) {
    choice(menuConvertNO_CONVERSION, "NO CONVERSION")
    choice(menuConvertSLOPE, "SLOPE")
    choice(menuConvertLINEAR, "LINEAR")
    choice(menuConverttypeKdegF, "typeKdegF")
    choice(menuConverttypeKdegC, "typeKdegC")
    choice(menuConverttypeJdegF, "typeJdegF")
    choice(menuConverttypeJdegC, "typeJdegC")
    choice(menuConverttypeEdegF, "typeEdegF(ixe only)")
    choice(menuConverttypeEdegC, "typeEdegC(ixe only)")
    choice(menuConverttypeTdegF, "typeTdegF")
    choice(menuConverttypeTdegC, "typeTdegC")
    choice(menuConverttypeRdegF, "typeRdegF")
    choice(menuConverttypeRdegC, "typeRdegC")
    choice(menuConverttypeSdegF, "typeSdegF")
    choice(menuConverttypeSdegC, "typeSdegC")
}
menu(serialIX) {
    choice(serialIX_unknown, "Unknown")
    choice(serialIX_No, "No")
    choice(serialIX_Yes, "Yes")
}
menu(menuYesNo) {
    choice(menuYesNoNO, "NO")
    choice(menuYesNoYES, "YES")
}
menu(serialDBIT) {
    choice(serialDBIT_unknown, "Unknown")
    choice(serialDBIT_5, "5")
    choice(serialDBIT_6, "6")
    choice(serialDBIT_7, "7")
    choice(serialDBIT_8, "8")
}
menu(selSELM) {
    choice(selSELM_Specified, "Specified")
    choice(selSELM_High_Signal, "High Signal")
    choice(selSELM_Low_Signal, "Low Signal")
    choice(selSELM_Median_Signal, "Median Signal")
}
recordtype(calcout) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %/* Declare Device Support Entry Table */
    %struct calcoutRecord;
    %typedef struct calcoutdset {
    %    dset common;
    %    long (*write)(struct calcoutRecord *prec);
    %} calcoutdset;
    %#define HAS_calcoutdset
    %
    %#include "dbScan.h"
    %#include "postfix.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(RPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct rpvtStruct *rpvt")
        interest(4)
        prompt("Record Private")
    }
    field(VAL, DBF_DOUBLE) {
        promptgroup("50 - Output")
        asl(ASL0)
        prompt("Result")
    }
    field(PVAL, DBF_DOUBLE) {
        prompt("Previous Value")
    }
    field(CALC, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_CALC)
        initial("0")
        pp(TRUE)
        size(80)
        prompt("Calculation")
    }
    field(CLCV, DBF_LONG) {
        interest(1)
        prompt("CALC Valid")
    }
    field(INPA, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        special(SPC_MOD)
        interest(1)
        prompt("Input A")
    }
    field(INPB, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        special(SPC_MOD)
        interest(1)
        prompt("Input B")
    }
    field(INPC, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        special(SPC_MOD)
        interest(1)
        prompt("Input C")
    }
    field(INPD, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        special(SPC_MOD)
        interest(1)
        prompt("Input D")
    }
    field(INPE, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        special(SPC_MOD)
        interest(1)
        prompt("Input E")
    }
    field(INPF, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        special(SPC_MOD)
        interest(1)
        prompt("Input F")
    }
    field(INPG, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        special(SPC_MOD)
        interest(1)
        prompt("Input G")
    }
    field(INPH, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        special(SPC_MOD)
        interest(1)
        prompt("Input H")
    }
    field(INPI, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        special(SPC_MOD)
        interest(1)
        prompt("Input I")
    }
    field(INPJ, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        special(SPC_MOD)
        interest(1)
        prompt("Input J")
    }
    field(INPK, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        special(SPC_MOD)
        interest(1)
        prompt("Input K")
    }
    field(INPL, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        special(SPC_MOD)
        interest(1)
        prompt("Input L")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        special(SPC_MOD)
        interest(1)
        prompt("Output Specification")
    }
    field(INAV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPA PV Status")
    }
    field(INBV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPB PV Status")
    }
    field(INCV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPC PV Status")
    }
    field(INDV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPD PV Status")
    }
    field(INEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPE PV Status")
    }
    field(INFV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPF PV Status")
    }
    field(INGV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPG PV Status")
    }
    field(INHV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPH PV Status")
    }
    field(INIV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPI PV Status")
    }
    field(INJV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPJ PV Status")
    }
    field(INKV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPK PV Status")
    }
    field(INLV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        initial("1")
        interest(1)
        prompt("INPL PV Status")
    }
    field(OUTV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(calcoutINAV)
        interest(1)
        prompt("OUT PV Status")
    }
    field(OOPT, DBF_MENU) {
        promptgroup("50 - Output")
        menu(calcoutOOPT)
        interest(1)
        prompt("Output Execute Opt")
    }
    field(ODLY, DBF_DOUBLE) {
        promptgroup("50 - Output")
        asl(ASL0)
        interest(1)
        prompt("Output Execute Delay")
    }
    field(DLYA, DBF_USHORT) {
        special(SPC_NOMOD)
        asl(ASL0)
        prompt("Output Delay Active")
    }
    field(DOPT, DBF_MENU) {
        promptgroup("30 - Action")
        menu(calcoutDOPT)
        interest(1)
        prompt("Output Data Opt")
    }
    field(OCAL, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_CALC)
        initial("0")
        pp(TRUE)
        size(80)
        prompt("Output Calculation")
    }
    field(OCLV, DBF_LONG) {
        interest(1)
        prompt("OCAL Valid")
    }
    field(OEVT, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_MOD)
        asl(ASL0)
        size(40)
        prompt("Event To Issue")
    }
    field(EPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("EVENTPVT epvt")
        interest(4)
        prompt("Event private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID output action")
    }
    field(IVOV, DBF_DOUBLE) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Rng")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(A, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input A")
    }
    field(B, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input B")
    }
    field(C, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input C")
    }
    field(D, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input D")
    }
    field(E, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input E")
    }
    field(F, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input F")
    }
    field(G, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input G")
    }
    field(H, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input H")
    }
    field(I, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input I")
    }
    field(J, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input J")
    }
    field(K, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input K")
    }
    field(L, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input L")
    }
    field(OVAL, DBF_DOUBLE) {
        asl(ASL0)
        prompt("Output Value")
    }
    field(LA, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of A")
    }
    field(LB, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of B")
    }
    field(LC, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of C")
    }
    field(LD, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of D")
    }
    field(LE, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of E")
    }
    field(LF, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of F")
    }
    field(LG, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of G")
    }
    field(LH, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of H")
    }
    field(LI, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of I")
    }
    field(LJ, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of J")
    }
    field(LK, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of K")
    }
    field(LL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of L")
    }
    field(POVL, DBF_DOUBLE) {
        asl(ASL0)
        prompt("Prev Value of OVAL")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(RPCL, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("char	rpcl[INFIX_TO_POSTFIX_SIZE(80)]")
        interest(4)
        prompt("Reverse Polish Calc")
    }
    field(ORPC, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("char	orpc[INFIX_TO_POSTFIX_SIZE(80)]")
        interest(4)
        prompt("Reverse Polish OCalc")
    }
}
device(calcout, CONSTANT, devCalcoutSoft, "Soft Channel")
device(calcout, CONSTANT, devCalcoutSoftCallback, "Async Soft Channel")
recordtype(state) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_STRING) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        size(20)
        prompt("Value")
    }
    field(OVAL, DBF_STRING) {
        special(SPC_NOMOD)
        interest(3)
        size(20)
        prompt("Prev Value")
    }
}
recordtype(histogram) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct histogramRecord;
    %typedef struct histogramdset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*read_histogram)(struct histogramRecord *prec); /*(0,2)=> success and add_count, don't add_count); if add_count then sgnl added to array*/
    %    long (*special_linconv)(struct histogramRecord *prec, int after);
    %} histogramdset;
    %#define HAS_histogramdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *	val")
        prompt("Value")
    }
    field(NELM, DBF_USHORT) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Num of Array Elements")
    }
    field(CSTA, DBF_SHORT) {
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Collection Status")
    }
    field(CMD, DBF_MENU) {
        special(SPC_CALC)
        asl(ASL0)
        menu(histogramCMD)
        interest(1)
        prompt("Collection Control")
    }
    field(ULIM, DBF_DOUBLE) {
        prop(YES)
        promptgroup("30 - Action")
        special(SPC_RESET)
        interest(1)
        prompt("Upper Signal Limit")
    }
    field(LLIM, DBF_DOUBLE) {
        prop(YES)
        promptgroup("30 - Action")
        special(SPC_RESET)
        interest(1)
        prompt("Lower Signal Limit ")
    }
    field(WDTH, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Element Width")
    }
    field(SGNL, DBF_DOUBLE) {
        special(SPC_MOD)
        prompt("Signal Value")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(SVL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Signal Value Location")
    }
    field(BPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt32 *bptr")
        interest(4)
        prompt("Buffer Pointer")
    }
    field(WDOG, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *  wdog")
        interest(4)
        prompt("Watchdog callback")
    }
    field(MDEL, DBF_SHORT) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Count Deadband")
    }
    field(MCNT, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Counts Since Monitor")
    }
    field(SDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        special(SPC_RESET)
        interest(1)
        prompt("Monitor Seconds Dband")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_DOUBLE) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(HOPR, DBF_ULONG) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_ULONG) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
}
device(histogram, CONSTANT, devHistogramSoft, "Soft Channel")
recordtype(lsi) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct lsiRecord;
    %typedef struct lsidset {
    %    dset common;
    %    long (*read_string)(struct lsiRecord *prec);
    %} lsidset;
    %#define HAS_lsidset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("char *val")
        pp(TRUE)
        prompt("Current Value")
    }
    field(OVAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        extra("char *oval")
        interest(3)
        prompt("Old Value")
    }
    field(SIZV, DBF_USHORT) {
        promptgroup("40 - Input")
        special(SPC_NOMOD)
        initial("41")
        interest(1)
        prompt("Size of buffers")
    }
    field(LEN, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Length of VAL")
    }
    field(OLEN, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Length of OVAL")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(menuPost)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(menuPost)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(lsi, CONSTANT, devLsiSoft, "Soft Channel")
device(lsi, INST_IO, devLsiEnviron, "getenv")
device(lsi, INST_IO, asynLsiOctetCmdResponse, "asynOctetCmdResponse")
device(lsi, INST_IO, asynLsiOctetWriteRead, "asynOctetWriteRead")
device(lsi, INST_IO, asynLsiOctetRead, "asynOctetRead")
recordtype(int64out) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct int64outRecord;
    %typedef struct int64outdset {
    %    dset common;
    %    long (*write_int64out)(struct int64outRecord *prec);
    %} int64outdset;
    %#define HAS_int64outdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_INT64) {
        promptgroup("50 - Output")
        asl(ASL0)
        pp(TRUE)
        prompt("Desired Output")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Units name")
    }
    field(DRVH, DBF_INT64) {
        prop(YES)
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Drive High Limit")
    }
    field(DRVL, DBF_INT64) {
        prop(YES)
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Drive Low Limit")
    }
    field(HOPR, DBF_INT64) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_INT64) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_INT64) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_INT64) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_INT64) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(LALM, DBF_INT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_INT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_INT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID output action")
    }
    field(IVOV, DBF_INT64) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
}
device(int64out, CONSTANT, devI64outSoft, "Soft Channel")
device(int64out, CONSTANT, devI64outSoftCallback, "Async Soft Channel")
device(int64out, INST_IO, asynInt64Out, "asynInt64")
recordtype(seq) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        asl(ASL0)
        pp(TRUE)
        prompt("Used to trigger")
    }
    field(SELM, DBF_MENU) {
        promptgroup("30 - Action")
        menu(seqSELM)
        interest(1)
        prompt("Select Mechanism")
    }
    field(SELN, DBF_USHORT) {
        initial("1")
        interest(1)
        prompt("Link Selection")
    }
    field(SELL, DBF_INLINK) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Link Selection Loc")
    }
    field(OFFS, DBF_SHORT) {
        promptgroup("30 - Action")
        initial("0")
        interest(1)
        prompt("Offset for Specified")
    }
    field(SHFT, DBF_SHORT) {
        promptgroup("30 - Action")
        initial("-1")
        interest(1)
        prompt("Shift for Mask mode")
    }
    field(OLDN, DBF_USHORT) {
        interest(4)
        prompt("Old Selection")
    }
    field(PREC, DBF_SHORT) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(DLY0, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 0")
    }
    field(DOL0, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 0")
    }
    field(DO0, DBF_DOUBLE) {
        interest(1)
        prompt("Value 0")
    }
    field(LNK0, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 0")
    }
    field(DLY1, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 1")
    }
    field(DOL1, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link1")
    }
    field(DO1, DBF_DOUBLE) {
        interest(1)
        prompt("Value 1")
    }
    field(LNK1, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 1")
    }
    field(DLY2, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 2")
    }
    field(DOL2, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 2")
    }
    field(DO2, DBF_DOUBLE) {
        interest(1)
        prompt("Value 2")
    }
    field(LNK2, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 2")
    }
    field(DLY3, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 3")
    }
    field(DOL3, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 3")
    }
    field(DO3, DBF_DOUBLE) {
        interest(1)
        prompt("Value 3")
    }
    field(LNK3, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 3")
    }
    field(DLY4, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 4")
    }
    field(DOL4, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 4")
    }
    field(DO4, DBF_DOUBLE) {
        interest(1)
        prompt("Value 4")
    }
    field(LNK4, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 4")
    }
    field(DLY5, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 5")
    }
    field(DOL5, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 5")
    }
    field(DO5, DBF_DOUBLE) {
        interest(1)
        prompt("Value 5")
    }
    field(LNK5, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 5")
    }
    field(DLY6, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 6")
    }
    field(DOL6, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 6")
    }
    field(DO6, DBF_DOUBLE) {
        interest(1)
        prompt("Value 6")
    }
    field(LNK6, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 6")
    }
    field(DLY7, DBF_DOUBLE) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Delay 7")
    }
    field(DOL7, DBF_INLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Input link 7")
    }
    field(DO7, DBF_DOUBLE) {
        interest(1)
        prompt("Value 7")
    }
    field(LNK7, DBF_OUTLINK) {
        promptgroup("41 - Link 0-7")
        interest(1)
        prompt("Output Link 7")
    }
    field(DLY8, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 8")
    }
    field(DOL8, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 8")
    }
    field(DO8, DBF_DOUBLE) {
        interest(1)
        prompt("Value 8")
    }
    field(LNK8, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 8")
    }
    field(DLY9, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 9")
    }
    field(DOL9, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 9")
    }
    field(DO9, DBF_DOUBLE) {
        interest(1)
        prompt("Value 9")
    }
    field(LNK9, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 9")
    }
    field(DLYA, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 10")
    }
    field(DOLA, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 10")
    }
    field(DOA, DBF_DOUBLE) {
        interest(1)
        prompt("Value 10")
    }
    field(LNKA, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 10")
    }
    field(DLYB, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 11")
    }
    field(DOLB, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 11")
    }
    field(DOB, DBF_DOUBLE) {
        interest(1)
        prompt("Value 11")
    }
    field(LNKB, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 11")
    }
    field(DLYC, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 12")
    }
    field(DOLC, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 12")
    }
    field(DOC, DBF_DOUBLE) {
        interest(1)
        prompt("Value 12")
    }
    field(LNKC, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 12")
    }
    field(DLYD, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 13")
    }
    field(DOLD, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 13")
    }
    field(DOD, DBF_DOUBLE) {
        interest(1)
        prompt("Value 13")
    }
    field(LNKD, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 13")
    }
    field(DLYE, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 14")
    }
    field(DOLE, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 14")
    }
    field(DOE, DBF_DOUBLE) {
        interest(1)
        prompt("Value 14")
    }
    field(LNKE, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 14")
    }
    field(DLYF, DBF_DOUBLE) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Delay 15")
    }
    field(DOLF, DBF_INLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Input link 15")
    }
    field(DOF, DBF_DOUBLE) {
        interest(1)
        prompt("Value 15")
    }
    field(LNKF, DBF_OUTLINK) {
        promptgroup("42 - Link 8-F")
        interest(1)
        prompt("Output Link 15")
    }
}
recordtype(stringout) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct stringoutRecord;
    %typedef struct stringoutdset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*write_stringout)(struct stringoutRecord *prec); /*(-1,0)=>(failure,success)*/
    %} stringoutdset;
    %#define HAS_stringoutdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_STRING) {
        promptgroup("50 - Output")
        asl(ASL0)
        pp(TRUE)
        size(40)
        prompt("Current Value")
    }
    field(OVAL, DBF_STRING) {
        special(SPC_NOMOD)
        interest(3)
        size(40)
        prompt("Previous Value")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(stringoutPOST)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(stringoutPOST)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID output action")
    }
    field(IVOV, DBF_STRING) {
        promptgroup("50 - Output")
        interest(2)
        size(40)
        prompt("INVALID output value")
    }
}
device(stringout, CONSTANT, devSoSoft, "Soft Channel")
device(stringout, CONSTANT, devSoSoftCallback, "Async Soft Channel")
device(stringout, INST_IO, devSoStdio, "stdio")
device(stringout, INST_IO, asynSoOctetWrite, "asynOctetWrite")
recordtype(aai) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct aaiRecord;
    %typedef struct aaidset {
    %    dset common; /*init_record returns: (-1,0,AAI_DEVINIT_PASS1)=>(failure,success,callback)*/
    %    long (*read_aai)(struct aaiRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} aaidset;
    %#define HAS_aaidset
    %#define AAI_DEVINIT_PASS1 2
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *		val")
        pp(TRUE)
        prompt("Value")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(NELM, DBF_ULONG) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Number of Elements")
    }
    field(FTVL, DBF_MENU) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        menu(menuFtype)
        interest(1)
        prompt("Field Type of Value")
    }
    field(NORD, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Number elements read")
    }
    field(BPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *		bptr")
        interest(4)
        prompt("Buffer Pointer")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(aaiPOST)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(aaiPOST)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(HASH, DBF_ULONG) {
        interest(3)
        prompt("Hash of OnChange data.")
    }
}
device(aai, CONSTANT, devAaiSoft, "Soft Channel")
device(aai, INST_IO, asynInt8ArrayAai, "asynInt8ArrayIn")
device(aai, INST_IO, asynInt16ArrayAai, "asynInt16ArrayIn")
device(aai, INST_IO, asynInt32ArrayAai, "asynInt32ArrayIn")
device(aai, INST_IO, asynFloat32ArrayAai, "asynFloat32ArrayIn")
device(aai, INST_IO, asynFloat64ArrayAai, "asynFloat64ArrayIn")
device(aai, INST_IO, asynInt64ArrayAai, "asynInt64ArrayIn")
recordtype(permissive) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_USHORT) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Status")
    }
    field(WFLG, DBF_USHORT) {
        pp(TRUE)
        prompt("Wait Flag")
    }
    field(LABL, DBF_STRING) {
        promptgroup("80 - Display")
        interest(1)
        pp(TRUE)
        size(20)
        prompt("Button Label")
    }
    field(OVAL, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Old Status")
    }
    field(OFLG, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Old Flag")
    }
}
recordtype(bo) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct boRecord;
    %typedef struct bodset {
    %    dset common; /*init_record returns:(0,2)=>(success,success no convert*/
    %    long (*write_bo)(struct boRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} bodset;
    %#define HAS_bodset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_ENUM) {
        promptgroup("50 - Output")
        asl(ASL0)
        pp(TRUE)
        prompt("Current Value")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(HIGH, DBF_DOUBLE) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Seconds to Hold High")
    }
    field(ZNAM, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Zero Name")
    }
    field(ONAM, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        pp(TRUE)
        size(26)
        prompt("One Name")
    }
    field(RVAL, DBF_ULONG) {
        pp(TRUE)
        prompt("Raw Value")
    }
    field(ORAW, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("prev Raw Value")
    }
    field(MASK, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Hardware Mask")
    }
    field(RPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *  rpvt")
        interest(4)
        prompt("Record Private")
    }
    field(WDPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *	wdpt")
        interest(4)
        prompt("Watch Dog Timer ID")
    }
    field(ZSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Zero Error Severity")
    }
    field(OSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("One Error Severity")
    }
    field(COSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Change of State Sevr")
    }
    field(RBV, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Readback Value")
    }
    field(ORBV, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Readback Value")
    }
    field(MLST, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
    field(LALM, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID outpt action")
    }
    field(IVOV, DBF_USHORT) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
}
device(bo, CONSTANT, devBoSoft, "Soft Channel")
device(bo, CONSTANT, devBoSoftRaw, "Raw Soft Channel")
device(bo, CONSTANT, devBoSoftCallback, "Async Soft Channel")
device(bo, INST_IO, devBoGeneralTime, "General Time")
device(bo, INST_IO, devBoDbState, "Db State")
device(bo, INST_IO, asynBoInt32, "asynInt32")
device(bo, INST_IO, asynBoUInt32Digital, "asynUInt32Digital")
device(bo, CAMAC_IO, devBoMch, "MCHsensor")
recordtype(dfanout) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_DOUBLE) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Desired Output")
    }
    field(SELM, DBF_MENU) {
        promptgroup("30 - Action")
        menu(dfanoutSELM)
        interest(1)
        prompt("Select Mechanism")
    }
    field(SELN, DBF_USHORT) {
        initial("1")
        interest(1)
        prompt("Link Selection")
    }
    field(SELL, DBF_INLINK) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Link Selection Loc")
    }
    field(OUTA, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec A")
    }
    field(OUTB, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec B")
    }
    field(OUTC, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec C")
    }
    field(OUTD, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec D")
    }
    field(OUTE, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec E")
    }
    field(OUTF, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec F")
    }
    field(OUTG, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec G")
    }
    field(OUTH, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Spec H")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
}
recordtype(mbbi) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %/* Declare Device Support Entry Table */
    %struct mbbiRecord;
    %typedef struct mbbidset {
    %    dset common; /* init_record returns: (-1,0) => (failure, success)*/
    %    long (*read_mbbi)(struct mbbiRecord *prec); /* (0, 2) => (success, success no convert)*/
    %} mbbidset;
    %#define HAS_mbbidset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_ENUM) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Current Value")
    }
    field(NOBT, DBF_USHORT) {
        promptgroup("40 - Input")
        special(SPC_NOMOD)
        interest(1)
        prompt("Number of Bits")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(ZRVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Zero Value")
    }
    field(ONVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("One Value")
    }
    field(TWVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Two Value")
    }
    field(THVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Three Value")
    }
    field(FRVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Four Value")
    }
    field(FVVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Five Value")
    }
    field(SXVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Six Value")
    }
    field(SVVL, DBF_ULONG) {
        base(HEX)
        promptgroup("41 - Input 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Seven Value")
    }
    field(EIVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Eight Value")
    }
    field(NIVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Nine Value")
    }
    field(TEVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Ten Value")
    }
    field(ELVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Eleven Value")
    }
    field(TVVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Twelve Value")
    }
    field(TTVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Thirteen Value")
    }
    field(FTVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Fourteen Value")
    }
    field(FFVL, DBF_ULONG) {
        base(HEX)
        promptgroup("42 - Input 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Fifteen Value")
    }
    field(ZRST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Zero String")
    }
    field(ONST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("One String")
    }
    field(TWST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Two String")
    }
    field(THST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Three String")
    }
    field(FRST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Four String")
    }
    field(FVST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Five String")
    }
    field(SXST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Six String")
    }
    field(SVST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Seven String")
    }
    field(EIST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Eight String")
    }
    field(NIST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Nine String")
    }
    field(TEST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Ten String")
    }
    field(ELST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Eleven String")
    }
    field(TVST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Twelve String")
    }
    field(TTST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Thirteen String")
    }
    field(FTST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Fourteen String")
    }
    field(FFST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Fifteen String")
    }
    field(ZRSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Zero Severity")
    }
    field(ONSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State One Severity")
    }
    field(TWSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Two Severity")
    }
    field(THSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Three Severity")
    }
    field(FRSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Four Severity")
    }
    field(FVSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Five Severity")
    }
    field(SXSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Six Severity")
    }
    field(SVSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Seven Severity")
    }
    field(EISV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Eight Severity")
    }
    field(NISV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Nine Severity")
    }
    field(TESV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Ten Severity")
    }
    field(ELSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Eleven Severity")
    }
    field(TVSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Twelve Severity")
    }
    field(TTSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Thirteen Sevr")
    }
    field(FTSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Fourteen Sevr")
    }
    field(FFSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Fifteen Severity")
    }
    field(AFTC, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Filter Time Constant")
    }
    field(AFVL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Alarm Filter Value")
    }
    field(UNSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Unknown State Severity")
    }
    field(COSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Change of State Svr")
    }
    field(RVAL, DBF_ULONG) {
        pp(TRUE)
        prompt("Raw Value")
    }
    field(ORAW, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Raw Value")
    }
    field(MASK, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Hardware Mask")
    }
    field(MLST, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
    field(LALM, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(SDEF, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("States Defined")
    }
    field(SHFT, DBF_USHORT) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Shift")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_ULONG) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuSimm)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(mbbi, CONSTANT, devMbbiSoft, "Soft Channel")
device(mbbi, CONSTANT, devMbbiSoftRaw, "Raw Soft Channel")
device(mbbi, CONSTANT, devMbbiSoftCallback, "Async Soft Channel")
device(mbbi, INST_IO, asynMbbiInt32, "asynInt32")
device(mbbi, INST_IO, asynMbbiUInt32Digital, "asynUInt32Digital")
device(mbbi, CAMAC_IO, devMbbiMch, "MCHsensor")
recordtype(event) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct eventRecord;
    %typedef struct eventdset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*read_event)(struct eventRecord *prec); /*(0)=> success */
    %} eventdset;
    %#define HAS_eventdset
    %
    %#include "dbScan.h"
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_STRING) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        asl(ASL0)
        size(40)
        prompt("Event Name To Post")
    }
    field(EPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("EVENTPVT epvt")
        interest(4)
        prompt("Event private")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Sim Input Specifctn")
    }
    field(SVAL, DBF_STRING) {
        size(40)
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Sim Mode Location")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Sim mode Alarm Svrty")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(event, CONSTANT, devEventSoft, "Soft Channel")
recordtype(compress) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *		val")
        pp(TRUE)
        prompt("Value")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(RES, DBF_SHORT) {
        special(SPC_RESET)
        asl(ASL0)
        interest(3)
        prompt("Reset")
    }
    field(ALG, DBF_MENU) {
        promptgroup("30 - Action")
        special(SPC_RESET)
        menu(compressALG)
        interest(1)
        prompt("Compression Algorithm")
    }
    field(BALG, DBF_MENU) {
        promptgroup("30 - Action")
        special(SPC_RESET)
        menu(bufferingALG)
        interest(1)
        prompt("Buffering Algorithm")
    }
    field(NSAM, DBF_ULONG) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Number of Values")
    }
    field(N, DBF_ULONG) {
        promptgroup("30 - Action")
        special(SPC_RESET)
        initial("1")
        interest(1)
        prompt("N to 1 Compression")
    }
    field(IHIL, DBF_DOUBLE) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Init High Interest Lim")
    }
    field(ILIL, DBF_DOUBLE) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Init Low Interest Lim")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(OFF, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Offset")
    }
    field(NUSE, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Number Used")
    }
    field(OUSE, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Old Number Used")
    }
    field(BPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("double		*bptr")
        interest(4)
        prompt("Buffer Pointer")
    }
    field(SPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("double		*sptr")
        interest(4)
        prompt("Summing Buffer Ptr")
    }
    field(WPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("double		*wptr")
        interest(4)
        prompt("Working Buffer Ptr")
    }
    field(INPN, DBF_LONG) {
        special(SPC_NOMOD)
        interest(4)
        prompt("Number of elements in Working Buffer")
    }
    field(CVB, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Compress Value Buffer")
    }
    field(INX, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Compressed Array Inx")
    }
}
recordtype(mbbo) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %/* Declare Device Support Entry Table */
    %struct mbboRecord;
    %typedef struct mbbodset {
    %    dset common; /*init_record returns: (0, 2) => (success, success no convert)*/
    %    long (*write_mbbo)(struct mbboRecord *prec); /*returns: (0, 2) => (success, success no convert)*/
    %} mbbodset;
    %#define HAS_mbbodset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_ENUM) {
        promptgroup("50 - Output")
        special(SPC_DBADDR)
        asl(ASL0)
        pp(TRUE)
        prompt("Desired Value")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(NOBT, DBF_USHORT) {
        promptgroup("50 - Output")
        special(SPC_NOMOD)
        interest(1)
        prompt("Number of Bits")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(ZRVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Zero Value")
    }
    field(ONVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("One Value")
    }
    field(TWVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Two Value")
    }
    field(THVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Three Value")
    }
    field(FRVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Four Value")
    }
    field(FVVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Five Value")
    }
    field(SXVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Six Value")
    }
    field(SVVL, DBF_ULONG) {
        base(HEX)
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Seven Value")
    }
    field(EIVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Eight Value")
    }
    field(NIVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Nine Value")
    }
    field(TEVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Ten Value")
    }
    field(ELVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Eleven Value")
    }
    field(TVVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Twelve Value")
    }
    field(TTVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Thirteen Value")
    }
    field(FTVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Fourteen Value")
    }
    field(FFVL, DBF_ULONG) {
        base(HEX)
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Fifteen Value")
    }
    field(ZRST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Zero String")
    }
    field(ONST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("One String")
    }
    field(TWST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Two String")
    }
    field(THST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Three String")
    }
    field(FRST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Four String")
    }
    field(FVST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Five String")
    }
    field(SXST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Six String")
    }
    field(SVST, DBF_STRING) {
        promptgroup("81 - Display 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Seven String")
    }
    field(EIST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Eight String")
    }
    field(NIST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Nine String")
    }
    field(TEST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Ten String")
    }
    field(ELST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Eleven String")
    }
    field(TVST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Twelve String")
    }
    field(TTST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Thirteen String")
    }
    field(FTST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Fourteen String")
    }
    field(FFST, DBF_STRING) {
        promptgroup("82 - Display 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Fifteen String")
    }
    field(ZRSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Zero Severity")
    }
    field(ONSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State One Severity")
    }
    field(TWSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Two Severity")
    }
    field(THSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Three Severity")
    }
    field(FRSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Four Severity")
    }
    field(FVSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Five Severity")
    }
    field(SXSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Six Severity")
    }
    field(SVSV, DBF_MENU) {
        promptgroup("71 - Alarm 0-7")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Seven Severity")
    }
    field(EISV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Eight Severity")
    }
    field(NISV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Nine Severity")
    }
    field(TESV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Ten Severity")
    }
    field(ELSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Eleven Severity")
    }
    field(TVSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Twelve Severity")
    }
    field(TTSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Thirteen Sevr")
    }
    field(FTSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Fourteen Sevr")
    }
    field(FFSV, DBF_MENU) {
        promptgroup("72 - Alarm 8-15")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("State Fifteen Sevr")
    }
    field(UNSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Unknown State Sevr")
    }
    field(COSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Change of State Sevr")
    }
    field(RVAL, DBF_ULONG) {
        pp(TRUE)
        prompt("Raw Value")
    }
    field(ORAW, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Raw Value")
    }
    field(RBV, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Readback Value")
    }
    field(ORBV, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Readback Value")
    }
    field(MASK, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Hardware Mask")
    }
    field(MLST, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
    field(LALM, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(SDEF, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("States Defined")
    }
    field(SHFT, DBF_USHORT) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Shift")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID outpt action")
    }
    field(IVOV, DBF_USHORT) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
}
device(mbbo, CONSTANT, devMbboSoft, "Soft Channel")
device(mbbo, CONSTANT, devMbboSoftRaw, "Raw Soft Channel")
device(mbbo, CONSTANT, devMbboSoftCallback, "Async Soft Channel")
device(mbbo, INST_IO, asynMbboInt32, "asynInt32")
device(mbbo, INST_IO, asynMbboUInt32Digital, "asynUInt32Digital")
device(mbbo, CAMAC_IO, devMbboMch, "MCHsensor")
recordtype(ao) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct aoRecord;
    %typedef struct aodset {
    %    dset common; /*init_record returns: (0,2)=>(success,success no convert)*/
    %    long (*write_ao)(struct aoRecord *prec); /*(0)=>(success ) */
    %    long (*special_linconv)(struct aoRecord *prec, int after);
    %} aodset;
    %#define HAS_aodset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_DOUBLE) {
        promptgroup("50 - Output")
        asl(ASL0)
        pp(TRUE)
        prompt("Desired Output")
    }
    field(OVAL, DBF_DOUBLE) {
        prompt("Output Value")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(OROC, DBF_DOUBLE) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Rate of Change")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(OIF, DBF_MENU) {
        promptgroup("50 - Output")
        menu(aoOIF)
        interest(1)
        prompt("Out Full/Incremental")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(LINR, DBF_MENU) {
        promptgroup("60 - Convert")
        special(SPC_LINCONV)
        menu(menuConvert)
        interest(1)
        pp(TRUE)
        prompt("Linearization")
    }
    field(EGUF, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        special(SPC_LINCONV)
        interest(1)
        pp(TRUE)
        prompt("Eng Units Full")
    }
    field(EGUL, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        special(SPC_LINCONV)
        interest(1)
        pp(TRUE)
        prompt("Eng Units Low")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(ROFF, DBF_ULONG) {
        interest(2)
        pp(TRUE)
        prompt("Raw Offset")
    }
    field(EOFF, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        interest(2)
        pp(TRUE)
        prompt("EGU to Raw Offset")
    }
    field(ESLO, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        initial("1")
        interest(2)
        pp(TRUE)
        prompt("EGU to Raw Slope")
    }
    field(DRVH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Drive High Limit")
    }
    field(DRVL, DBF_DOUBLE) {
        prop(YES)
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Drive Low Limit")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(AOFF, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        interest(1)
        pp(TRUE)
        prompt("Adjustment Offset")
    }
    field(ASLO, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        interest(1)
        pp(TRUE)
        prompt("Adjustment Slope")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(RVAL, DBF_LONG) {
        pp(TRUE)
        prompt("Current Raw Value")
    }
    field(ORAW, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Previous Raw Value")
    }
    field(RBV, DBF_LONG) {
        special(SPC_NOMOD)
        prompt("Readback Value")
    }
    field(ORBV, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Readback Value")
    }
    field(PVAL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Previous value")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(PBRK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *   pbrk")
        interest(4)
        prompt("Ptrto brkTable")
    }
    field(INIT, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Initialized?")
    }
    field(LBRK, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("LastBreak Point")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID output action")
    }
    field(IVOV, DBF_DOUBLE) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
    field(OMOD, DBF_UCHAR) {
        special(SPC_NOMOD)
        prompt("Was OVAL modified?")
    }
}
device(ao, CONSTANT, devAoSoft, "Soft Channel")
device(ao, CONSTANT, devAoSoftRaw, "Raw Soft Channel")
device(ao, CONSTANT, devAoSoftCallback, "Async Soft Channel")
device(ao, INST_IO, asynAoInt32, "asynInt32")
device(ao, INST_IO, asynAoFloat64, "asynFloat64")
device(ao, INST_IO, asynAoInt64, "asynInt64")
device(ao, INST_IO, devAoStats, "IOC stats")
recordtype(aao) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct aaoRecord;
    %typedef struct aaodset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*write_aao)(struct aaoRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} aaodset;
    %#define HAS_aaodset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *		val")
        pp(TRUE)
        prompt("Value")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(NELM, DBF_ULONG) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Number of Elements")
    }
    field(FTVL, DBF_MENU) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        menu(menuFtype)
        interest(1)
        prompt("Field Type of Value")
    }
    field(NORD, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Number elements read")
    }
    field(BPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *		bptr")
        interest(4)
        prompt("Buffer Pointer")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(aaoPOST)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(aaoPOST)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(HASH, DBF_ULONG) {
        interest(3)
        prompt("Hash of OnChange data.")
    }
}
device(aao, CONSTANT, devAaoSoft, "Soft Channel")
device(aao, INST_IO, asynInt8ArrayAao, "asynInt8ArrayOut")
device(aao, INST_IO, asynInt16ArrayAao, "asynInt16ArrayOut")
device(aao, INST_IO, asynInt32ArrayAao, "asynInt32ArrayOut")
device(aao, INST_IO, asynFloat32ArrayAao, "asynFloat32ArrayOut")
device(aao, INST_IO, asynFloat64ArrayAao, "asynFloat64ArrayOut")
device(aao, INST_IO, asynInt64ArrayAao, "asynInt64ArrayOut")
recordtype(mbbiDirect) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %/* Declare Device Support Entry Table */
    %struct mbbiDirectRecord;
    %typedef struct mbbidirectdset {
    %    dset common; /* init_record returns: (-1,0) => (failure, success)*/
    %    long (*read_mbbi)(struct mbbiDirectRecord *prec); /* (0, 2) => (success, success no convert)*/
    %} mbbidirectdset;
    %#define HAS_mbbidirectdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Current Value")
    }
    field(NOBT, DBF_SHORT) {
        promptgroup("40 - Input")
        special(SPC_NOMOD)
        interest(1)
        prompt("Number of Bits")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(RVAL, DBF_ULONG) {
        pp(TRUE)
        prompt("Raw Value")
    }
    field(ORAW, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Raw Value")
    }
    field(MASK, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Hardware Mask")
    }
    field(MLST, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
    field(SHFT, DBF_USHORT) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Shift")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_LONG) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuSimm)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(B0, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 0")
    }
    field(B1, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 1")
    }
    field(B2, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 2")
    }
    field(B3, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 3")
    }
    field(B4, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 4")
    }
    field(B5, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 5")
    }
    field(B6, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 6")
    }
    field(B7, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 7")
    }
    field(B8, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 8")
    }
    field(B9, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 9")
    }
    field(BA, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 10")
    }
    field(BB, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 11")
    }
    field(BC, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 12")
    }
    field(BD, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 13")
    }
    field(BE, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 14")
    }
    field(BF, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 15")
    }
    field(B10, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 16")
    }
    field(B11, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 17")
    }
    field(B12, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 18")
    }
    field(B13, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 19")
    }
    field(B14, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 20")
    }
    field(B15, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 21")
    }
    field(B16, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 22")
    }
    field(B17, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 23")
    }
    field(B18, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 24")
    }
    field(B19, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 25")
    }
    field(B1A, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 26")
    }
    field(B1B, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 27")
    }
    field(B1C, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 28")
    }
    field(B1D, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 29")
    }
    field(B1E, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 30")
    }
    field(B1F, DBF_UCHAR) {
        interest(1)
        pp(TRUE)
        prompt("Bit 31")
    }
}
device(mbbiDirect, CONSTANT, devMbbiDirectSoft, "Soft Channel")
device(mbbiDirect, CONSTANT, devMbbiDirectSoftRaw, "Raw Soft Channel")
device(mbbiDirect, CONSTANT, devMbbiDirectSoftCallback, "Async Soft Channel")
device(mbbiDirect, INST_IO, asynMbbiDirectUInt32Digital, "asynUInt32Digital")
recordtype(asyn) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        asl(ASL0)
        interest(4)
        prompt("Value field (unused)")
    }
    field(PORT, DBF_STRING) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        initial("")
        interest(1)
        size(40)
        prompt("asyn port")
    }
    field(ADDR, DBF_LONG) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        initial("0")
        interest(1)
        prompt("asyn address")
    }
    field(PCNCT, DBF_MENU) {
        special(SPC_MOD)
        menu(asynCONNECT)
        interest(2)
        prompt("Port Connect/Disconnect")
    }
    field(DRVINFO, DBF_STRING) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        initial("")
        interest(2)
        size(40)
        prompt("Driver info string")
    }
    field(REASON, DBF_LONG) {
        special(SPC_MOD)
        interest(2)
        prompt("asynUser->reason")
    }
    field(TMOD, DBF_MENU) {
        promptgroup("40 - Input")
        menu(asynTMOD)
        interest(1)
        prompt("Transaction mode")
    }
    field(TMOT, DBF_DOUBLE) {
        promptgroup("40 - Input")
        initial("1.0")
        interest(1)
        prompt("Timeout (sec)")
    }
    field(IFACE, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(asynINTERFACE)
        interest(2)
        prompt("Interface")
    }
    field(OCTETIV, DBF_LONG) {
        interest(2)
        prompt("asynOctet is valid")
    }
    field(OPTIONIV, DBF_LONG) {
        interest(2)
        prompt("asynOption is valid")
    }
    field(GPIBIV, DBF_LONG) {
        interest(2)
        prompt("asynGPIB is valid")
    }
    field(I32IV, DBF_LONG) {
        interest(2)
        prompt("asynInt32 is valid")
    }
    field(UI32IV, DBF_LONG) {
        interest(2)
        prompt("asynUInt32Digital is valid")
    }
    field(F64IV, DBF_LONG) {
        interest(2)
        prompt("asynFloat64 is valid")
    }
    field(AOUT, DBF_STRING) {
        promptgroup("50 - Output")
        interest(1)
        pp(TRUE)
        size(40)
        prompt("Output (command) string")
    }
    field(OEOS, DBF_STRING) {
        promptgroup("50 - Output")
        special(SPC_MOD)
        interest(1)
        size(40)
        prompt("Output delimiter")
    }
    field(BOUT, DBF_CHAR) {
        special(SPC_DBADDR)
        interest(1)
        pp(TRUE)
        prompt("Output binary data")
    }
    field(OPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *optr")
        interest(4)
        prompt("Output buffer pointer")
    }
    field(OMAX, DBF_LONG) {
        promptgroup("50 - Output")
        special(SPC_NOMOD)
        initial("80")
        interest(1)
        prompt("Max. size of output array")
    }
    field(NOWT, DBF_LONG) {
        promptgroup("50 - Output")
        initial("80")
        interest(1)
        prompt("Number of bytes to write")
    }
    field(NAWT, DBF_LONG) {
        interest(1)
        prompt("Number of bytes actually written")
    }
    field(OFMT, DBF_MENU) {
        promptgroup("50 - Output")
        menu(asynFMT)
        interest(1)
        prompt("Output format")
    }
    field(AINP, DBF_STRING) {
        special(SPC_NOMOD)
        interest(1)
        size(40)
        prompt("Input (response) string")
    }
    field(TINP, DBF_STRING) {
        special(SPC_NOMOD)
        asl(ASL0)
        interest(1)
        size(40)
        prompt("Translated input string")
    }
    field(IEOS, DBF_STRING) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        interest(1)
        size(40)
        prompt("Input Delimiter")
    }
    field(BINP, DBF_CHAR) {
        special(SPC_DBADDR)
        asl(ASL0)
        prompt("Input binary data")
    }
    field(IPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *iptr")
        interest(4)
        size(4)
        prompt("Input buffer pointer")
    }
    field(IMAX, DBF_LONG) {
        promptgroup("40 - Input")
        special(SPC_NOMOD)
        initial("80")
        interest(1)
        prompt("Max. size of input array")
    }
    field(NRRD, DBF_LONG) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Number of bytes to read")
    }
    field(NORD, DBF_LONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Number of bytes read")
    }
    field(IFMT, DBF_MENU) {
        promptgroup("40 - Input")
        menu(asynFMT)
        interest(1)
        prompt("Input format")
    }
    field(EOMR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(asynEOMREASON)
        interest(1)
        prompt("EOM reason")
    }
    field(I32INP, DBF_LONG) {
        special(SPC_NOMOD)
        interest(2)
        prompt("asynInt32 input")
    }
    field(I32OUT, DBF_LONG) {
        promptgroup("50 - Output")
        interest(2)
        pp(TRUE)
        prompt("asynInt32 output")
    }
    field(UI32INP, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(2)
        prompt("asynUInt32Digital input")
    }
    field(UI32OUT, DBF_ULONG) {
        promptgroup("50 - Output")
        interest(2)
        pp(TRUE)
        prompt("asynUInt32Digital output")
    }
    field(UI32MASK, DBF_ULONG) {
        promptgroup("50 - Output")
        special(SPC_MOD)
        interest(2)
        initial("0xffffffff")
        prompt("asynUInt32Digital mask")
    }
    field(F64INP, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(2)
        prompt("asynFloat64 input")
    }
    field(F64OUT, DBF_DOUBLE) {
        promptgroup("50 - Output")
        interest(2)
        pp(TRUE)
        prompt("asynFloat64 output")
    }
    field(BAUD, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialBAUD)
        interest(2)
        prompt("Baud rate")
    }
    field(LBAUD, DBF_LONG) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        interest(2)
        prompt("Baud rate")
    }
    field(PRTY, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialPRTY)
        interest(2)
        prompt("Parity")
    }
    field(DBIT, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialDBIT)
        interest(2)
        prompt("Data bits")
    }
    field(SBIT, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialSBIT)
        interest(2)
        prompt("Stop bits")
    }
    field(MCTL, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialMCTL)
        interest(2)
        prompt("Modem control")
    }
    field(FCTL, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialFCTL)
        interest(2)
        prompt("Flow control")
    }
    field(IXON, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialIX)
        interest(2)
        prompt("Output XON/XOFF")
    }
    field(IXOFF, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialIX)
        interest(2)
        prompt("Input XON/XOFF")
    }
    field(IXANY, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(serialIX)
        interest(2)
        prompt("XON=any character")
    }
    field(HOSTINFO, DBF_STRING) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        initial("")
        interest(1)
        size(40)
        prompt("host info")
    }
    field(DRTO, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(ipDRTO)
        interest(2)
        prompt("Disconnect on timeout")
    }
    field(UCMD, DBF_MENU) {
        promptgroup("50 - Output")
        menu(gpibUCMD)
        interest(2)
        pp(TRUE)
        prompt("Universal command")
    }
    field(ACMD, DBF_MENU) {
        promptgroup("50 - Output")
        menu(gpibACMD)
        interest(2)
        pp(TRUE)
        prompt("Addressed command")
    }
    field(SPR, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Serial poll response")
    }
    field(TMSK, DBF_LONG) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        interest(1)
        prompt("Trace mask")
    }
    field(TB0, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace error")
    }
    field(TB1, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace IO device")
    }
    field(TB2, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace IO filter")
    }
    field(TB3, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace IO driver")
    }
    field(TB4, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace flow")
    }
    field(TB5, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace warning")
    }
    field(TIOM, DBF_LONG) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        interest(1)
        prompt("Trace I/O mask")
    }
    field(TIB0, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace IO ASCII")
    }
    field(TIB1, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace IO escape")
    }
    field(TIB2, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace IO hex")
    }
    field(TINM, DBF_LONG) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        interest(1)
        prompt("Trace Info mask")
    }
    field(TINB0, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace Info Time")
    }
    field(TINB1, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace Info Port")
    }
    field(TINB2, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace Info Source")
    }
    field(TINB3, DBF_MENU) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        menu(asynTRACE)
        interest(1)
        prompt("Trace Info Thread")
    }
    field(TSIZ, DBF_LONG) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        interest(1)
        prompt("Trace IO truncate size")
    }
    field(TFIL, DBF_STRING) {
        promptgroup("80 - Display")
        special(SPC_MOD)
        interest(1)
        size(40)
        prompt("Trace IO file")
    }
    field(AUCT, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(asynAUTOCONNECT)
        interest(1)
        prompt("Autoconnect")
    }
    field(CNCT, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(asynCONNECT)
        interest(1)
        prompt("Connect/Disconnect")
    }
    field(ENBL, DBF_MENU) {
        promptgroup("40 - Input")
        special(SPC_MOD)
        menu(asynENABLE)
        interest(1)
        prompt("Enable/Disable")
    }
    field(ERRS, DBF_NOACCESS) {
        special(SPC_DBADDR)
        extra("char *errs")
        interest(4)
        prompt("Error string")
    }
    field(AQR, DBF_UCHAR) {
        special(SPC_MOD)
        interest(4)
        prompt("Abort queueRequest")
    }
}
device(asyn, INST_IO, asynRecordDevice, "asynRecordDevice")
recordtype(waveform) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct waveformRecord;
    %typedef struct wfdset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*read_wf)(struct waveformRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} wfdset;
    %#define HAS_wfdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *		val")
        pp(TRUE)
        prompt("Value")
    }
    field(RARM, DBF_SHORT) {
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Rearm the waveform")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(NELM, DBF_ULONG) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Number of Elements")
    }
    field(FTVL, DBF_MENU) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        menu(menuFtype)
        interest(1)
        prompt("Field Type of Value")
    }
    field(BUSY, DBF_SHORT) {
        special(SPC_NOMOD)
        prompt("Busy Indicator")
    }
    field(NORD, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Number elements read")
    }
    field(BPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *		bptr")
        interest(4)
        prompt("Buffer Pointer")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(waveformPOST)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(waveformPOST)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(HASH, DBF_ULONG) {
        interest(3)
        prompt("Hash of OnChange data.")
    }
}
device(waveform, CONSTANT, devWfSoft, "Soft Channel")
device(waveform, INST_IO, asynWfOctetCmdResponse, "asynOctetCmdResponse")
device(waveform, INST_IO, asynWfOctetWriteRead, "asynOctetWriteRead")
device(waveform, INST_IO, asynWfOctetRead, "asynOctetRead")
device(waveform, INST_IO, asynWfOctetWrite, "asynOctetWrite")
device(waveform, INST_IO, asynWfOctetWriteBinary, "asynOctetWriteBinary")
device(waveform, INST_IO, asynInt8ArrayWfIn, "asynInt8ArrayIn")
device(waveform, INST_IO, asynInt8ArrayWfOut, "asynInt8ArrayOut")
device(waveform, INST_IO, asynInt16ArrayWfIn, "asynInt16ArrayIn")
device(waveform, INST_IO, asynInt16ArrayWfOut, "asynInt16ArrayOut")
device(waveform, INST_IO, asynInt32ArrayWfIn, "asynInt32ArrayIn")
device(waveform, INST_IO, asynInt32ArrayWfOut, "asynInt32ArrayOut")
device(waveform, INST_IO, asynFloat32ArrayWfIn, "asynFloat32ArrayIn")
device(waveform, INST_IO, asynFloat32ArrayWfOut, "asynFloat32ArrayOut")
device(waveform, INST_IO, asynFloat64ArrayWfIn, "asynFloat64ArrayIn")
device(waveform, INST_IO, asynFloat64ArrayWfOut, "asynFloat64ArrayOut")
device(waveform, INST_IO, asynInt32TimeSeries, "asynInt32TimeSeries")
device(waveform, INST_IO, asynFloat64TimeSeries, "asynFloat64TimeSeries")
device(waveform, INST_IO, asynInt64ArrayWfIn, "asynInt64ArrayIn")
device(waveform, INST_IO, asynInt64ArrayWfOut, "asynInt64ArrayOut")
device(waveform, INST_IO, asynInt64TimeSeries, "asynInt64TimeSeries")
device(waveform, INST_IO, devWaveformStats, "IOC stats")
recordtype(fanout) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        asl(ASL0)
        pp(TRUE)
        prompt("Used to trigger")
    }
    field(SELM, DBF_MENU) {
        promptgroup("30 - Action")
        menu(fanoutSELM)
        interest(1)
        prompt("Select Mechanism")
    }
    field(SELN, DBF_USHORT) {
        initial("1")
        interest(1)
        prompt("Link Selection")
    }
    field(SELL, DBF_INLINK) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Link Selection Loc")
    }
    field(OFFS, DBF_SHORT) {
        promptgroup("30 - Action")
        initial("0")
        interest(1)
        prompt("Offset for Specified")
    }
    field(SHFT, DBF_SHORT) {
        promptgroup("30 - Action")
        initial("-1")
        interest(1)
        prompt("Shift for Mask mode")
    }
    field(LNK0, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 0")
    }
    field(LNK1, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 1")
    }
    field(LNK2, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 2")
    }
    field(LNK3, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 3")
    }
    field(LNK4, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 4")
    }
    field(LNK5, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 5")
    }
    field(LNK6, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 6")
    }
    field(LNK7, DBF_FWDLINK) {
        promptgroup("51 - Output 0-7")
        interest(1)
        prompt("Forward Link 7")
    }
    field(LNK8, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 8")
    }
    field(LNK9, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 9")
    }
    field(LNKA, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 10")
    }
    field(LNKB, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 11")
    }
    field(LNKC, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 12")
    }
    field(LNKD, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 13")
    }
    field(LNKE, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 14")
    }
    field(LNKF, DBF_FWDLINK) {
        promptgroup("52 - Output 8-F")
        interest(1)
        prompt("Forward Link 15")
    }
}
recordtype(longin) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct longinRecord;
    %typedef struct longindset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*read_longin)(struct longinRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} longindset;
    %#define HAS_longindset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Current value")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_LONG) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_LONG) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_LONG) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(AFTC, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Filter Time Constant")
    }
    field(AFVL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Alarm Filter Value")
    }
    field(ADEL, DBF_LONG) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_LONG) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(LALM, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Sim Input Specifctn")
    }
    field(SVAL, DBF_LONG) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Sim Mode Location")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Sim mode Alarm Svrty")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(longin, CONSTANT, devLiSoft, "Soft Channel")
device(longin, CONSTANT, devLiSoftCallback, "Async Soft Channel")
device(longin, INST_IO, devLiGeneralTime, "General Time")
device(longin, INST_IO, asynLiInt32, "asynInt32")
device(longin, INST_IO, asynLiUInt32Digital, "asynUInt32Digital")
device(longin, INST_IO, asynLiInt64, "asynInt64")
device(longin, CAMAC_IO, devLonginMch, "MCHsensor")
recordtype(printf) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct printfRecord;
    %typedef struct printfdset {
    %    dset common;
    %    long (*write_string)(struct printfRecord *prec);
    %} printfdset;
    %#define HAS_printfdset
    %
    %/* Number of INPx fields defined */
    %#define PRINTF_NLINKS 10
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("char *val")
        pp(TRUE)
        prompt("Result")
    }
    field(SIZV, DBF_USHORT) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("41")
        interest(1)
        prompt("Size of VAL buffer")
    }
    field(LEN, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Length of VAL")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(FMT, DBF_STRING) {
        promptgroup("30 - Action")
        pp(TRUE)
        size(81)
        prompt("Format String")
    }
    field(IVLS, DBF_STRING) {
        promptgroup("30 - Action")
        initial("LNK")
        size(16)
        prompt("Invalid Link String")
    }
    field(INP0, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 0")
    }
    field(INP1, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 1")
    }
    field(INP2, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 2")
    }
    field(INP3, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 3")
    }
    field(INP4, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 4")
    }
    field(INP5, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 5")
    }
    field(INP6, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 6")
    }
    field(INP7, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 7")
    }
    field(INP8, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 8")
    }
    field(INP9, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input 9")
    }
}
device(printf, CONSTANT, devPrintfSoft, "Soft Channel")
device(printf, CONSTANT, devPrintfSoftCallback, "Async Soft Channel")
device(printf, INST_IO, devPrintfStdio, "stdio")
device(printf, INST_IO, asynPfOctetWrite, "asynOctetWrite")
recordtype(sel) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_DOUBLE) {
        promptgroup("40 - Input")
        special(SPC_NOMOD)
        asl(ASL0)
        prompt("Result")
    }
    field(SELM, DBF_MENU) {
        promptgroup("30 - Action")
        menu(selSELM)
        prompt("Select Mechanism")
    }
    field(SELN, DBF_USHORT) {
        prompt("Index value")
    }
    field(PREC, DBF_SHORT) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(NVL, DBF_INLINK) {
        promptgroup("30 - Action")
        interest(1)
        prompt("Index Value Location")
    }
    field(INPA, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input A")
    }
    field(INPB, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input B")
    }
    field(INPC, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input C")
    }
    field(INPD, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input D")
    }
    field(INPE, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input E")
    }
    field(INPF, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input F")
    }
    field(INPG, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input G")
    }
    field(INPH, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input H")
    }
    field(INPI, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input I")
    }
    field(INPJ, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input J")
    }
    field(INPK, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input K")
    }
    field(INPL, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input L")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Rng")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(A, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input A")
    }
    field(B, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input B")
    }
    field(C, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input C")
    }
    field(D, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input D")
    }
    field(E, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input E")
    }
    field(F, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input F")
    }
    field(G, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input G")
    }
    field(H, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input H")
    }
    field(I, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input I")
    }
    field(J, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input J")
    }
    field(K, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input K")
    }
    field(L, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input L")
    }
    field(LA, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of A")
    }
    field(LB, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of B")
    }
    field(LC, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of C")
    }
    field(LD, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of D")
    }
    field(LE, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of E")
    }
    field(LF, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of F")
    }
    field(LG, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of G")
    }
    field(LH, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of H")
    }
    field(LI, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of I")
    }
    field(LJ, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of J")
    }
    field(LK, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of K")
    }
    field(LL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of L")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(NLST, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Index Monitored")
    }
}
recordtype(bi) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct biRecord;
    %typedef struct bidset {
    %    dset common;
    %    long (*read_bi)(struct biRecord *prec);
    %} bidset;
    %#define HAS_bidset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(VAL, DBF_ENUM) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Current Value")
    }
    field(ZSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Zero Error Severity")
    }
    field(OSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("One Error Severity")
    }
    field(COSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Change of State Svr")
    }
    field(ZNAM, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        pp(TRUE)
        size(26)
        prompt("Zero Name")
    }
    field(ONAM, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        pp(TRUE)
        size(26)
        prompt("One Name")
    }
    field(RVAL, DBF_ULONG) {
        pp(TRUE)
        prompt("Raw Value")
    }
    field(ORAW, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("prev Raw Value")
    }
    field(MASK, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Hardware Mask")
    }
    field(LALM, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(MLST, DBF_USHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_ULONG) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuSimm)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(bi, CONSTANT, devBiSoft, "Soft Channel")
device(bi, CONSTANT, devBiSoftRaw, "Raw Soft Channel")
device(bi, CONSTANT, devBiSoftCallback, "Async Soft Channel")
device(bi, INST_IO, devBiDbState, "Db State")
device(bi, INST_IO, asynBiInt32, "asynInt32")
device(bi, INST_IO, asynBiUInt32Digital, "asynUInt32Digital")
device(bi, CAMAC_IO, devBiMch, "MCHsensor")
recordtype(lso) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct lsoRecord;
    %typedef struct lsodset {
    %    dset common;
    %    long (*write_string)(struct lsoRecord *prec);
    %} lsodset;
    %#define HAS_lsodset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("char *val")
        pp(TRUE)
        prompt("Current Value")
    }
    field(OVAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        extra("char *oval")
        interest(3)
        prompt("Previous Value")
    }
    field(SIZV, DBF_USHORT) {
        promptgroup("50 - Output")
        special(SPC_NOMOD)
        initial("41")
        interest(1)
        prompt("Size of buffers")
    }
    field(LEN, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Length of VAL")
    }
    field(OLEN, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Length of OVAL")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Link")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID Output Action")
    }
    field(IVOV, DBF_STRING) {
        promptgroup("50 - Output")
        interest(2)
        size(40)
        prompt("INVALID Output Value")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(menuPost)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(menuPost)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(lso, CONSTANT, devLsoSoft, "Soft Channel")
device(lso, CONSTANT, devLsoSoftCallback, "Async Soft Channel")
device(lso, INST_IO, devLsoStdio, "stdio")
device(lso, INST_IO, asynLsoOctetWrite, "asynOctetWrite")
recordtype(subArray) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct subArrayRecord;
    %typedef struct sadset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*read_sa)(struct subArrayRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} sadset;
    %#define HAS_sadset
    %
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *		val")
        pp(TRUE)
        prompt("Value")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(FTVL, DBF_MENU) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        menu(menuFtype)
        interest(1)
        prompt("Field Type of Value")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(MALM, DBF_ULONG) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Maximum Elements")
    }
    field(NELM, DBF_ULONG) {
        promptgroup("30 - Action")
        initial("1")
        pp(TRUE)
        prompt("Number of Elements")
    }
    field(INDX, DBF_ULONG) {
        promptgroup("30 - Action")
        pp(TRUE)
        prompt("Substring Index")
    }
    field(BUSY, DBF_SHORT) {
        special(SPC_NOMOD)
        prompt("Busy Indicator")
    }
    field(NORD, DBF_LONG) {
        special(SPC_NOMOD)
        prompt("Number elements read")
    }
    field(BPTR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *		bptr")
        interest(4)
        prompt("Buffer Pointer")
    }
}
device(subArray, CONSTANT, devSASoft, "Soft Channel")
recordtype(calc) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %#include "postfix.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_DOUBLE) {
        promptgroup("50 - Output")
        asl(ASL0)
        prompt("Result")
    }
    field(CALC, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_CALC)
        initial("0")
        pp(TRUE)
        size(80)
        prompt("Calculation")
    }
    field(INPA, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input A")
    }
    field(INPB, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input B")
    }
    field(INPC, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input C")
    }
    field(INPD, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input D")
    }
    field(INPE, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input E")
    }
    field(INPF, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input F")
    }
    field(INPG, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input G")
    }
    field(INPH, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input H")
    }
    field(INPI, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input I")
    }
    field(INPJ, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input J")
    }
    field(INPK, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input K")
    }
    field(INPL, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input L")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Rng")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(AFTC, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Filter Time Constant")
    }
    field(AFVL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Alarm Filter Value")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(A, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input A")
    }
    field(B, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input B")
    }
    field(C, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input C")
    }
    field(D, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input D")
    }
    field(E, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input E")
    }
    field(F, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input F")
    }
    field(G, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input G")
    }
    field(H, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input H")
    }
    field(I, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input I")
    }
    field(J, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input J")
    }
    field(K, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input K")
    }
    field(L, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input L")
    }
    field(LA, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of A")
    }
    field(LB, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of B")
    }
    field(LC, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of C")
    }
    field(LD, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of D")
    }
    field(LE, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of E")
    }
    field(LF, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of F")
    }
    field(LG, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of G")
    }
    field(LH, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of H")
    }
    field(LI, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of I")
    }
    field(LJ, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of J")
    }
    field(LK, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of K")
    }
    field(LL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of L")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(RPCL, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("char	rpcl[INFIX_TO_POSTFIX_SIZE(80)]")
        interest(4)
        prompt("Reverse Polish Calc")
    }
}
recordtype(mbboDirect) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %/* Declare Device Support Entry Table */
    %struct mbboDirectRecord;
    %typedef struct mbbodirectdset {
    %    dset common; /*init_record returns: (0, 2)=>(success, success no convert)*/
    %    long (*write_mbbo)(struct mbboDirectRecord *prec); /*returns: (0, 2)=>(success, success no convert)*/
    %} mbbodirectdset;
    %#define HAS_mbbodirectdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        promptgroup("50 - Output")
        asl(ASL0)
        pp(TRUE)
        prompt("Word")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        special(SPC_RESET)
        menu(menuOmsl)
        interest(1)
        pp(TRUE)
        prompt("Output Mode Select")
    }
    field(NOBT, DBF_SHORT) {
        promptgroup("50 - Output")
        special(SPC_NOMOD)
        interest(1)
        prompt("Number of Bits")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(RVAL, DBF_ULONG) {
        special(SPC_NOMOD)
        pp(TRUE)
        prompt("Raw Value")
    }
    field(ORAW, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Raw Value")
    }
    field(RBV, DBF_ULONG) {
        special(SPC_NOMOD)
        prompt("Readback Value")
    }
    field(ORBV, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Readback Value")
    }
    field(MASK, DBF_ULONG) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Hardware Mask")
    }
    field(MLST, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
    field(SHFT, DBF_USHORT) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Shift")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Output Link")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID outpt action")
    }
    field(IVOV, DBF_LONG) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
    field(B0, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 0")
    }
    field(B1, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 1")
    }
    field(B2, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 2")
    }
    field(B3, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 3")
    }
    field(B4, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 4")
    }
    field(B5, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 5")
    }
    field(B6, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 6")
    }
    field(B7, DBF_UCHAR) {
        promptgroup("51 - Output 0-7")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 7")
    }
    field(B8, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 8")
    }
    field(B9, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 9")
    }
    field(BA, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 10")
    }
    field(BB, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 11")
    }
    field(BC, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 12")
    }
    field(BD, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 13")
    }
    field(BE, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 14")
    }
    field(BF, DBF_UCHAR) {
        promptgroup("52 - Output 8-15")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 15")
    }
    field(B10, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 16")
    }
    field(B11, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 17")
    }
    field(B12, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 18")
    }
    field(B13, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 19")
    }
    field(B14, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 20")
    }
    field(B15, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 21")
    }
    field(B16, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 22")
    }
    field(B17, DBF_UCHAR) {
        promptgroup("53 - Output 16-23")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 23")
    }
    field(B18, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 24")
    }
    field(B19, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 25")
    }
    field(B1A, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 26")
    }
    field(B1B, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 27")
    }
    field(B1C, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 28")
    }
    field(B1D, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 29")
    }
    field(B1E, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 30")
    }
    field(B1F, DBF_UCHAR) {
        promptgroup("54 - Output 24-31")
        special(SPC_MOD)
        interest(1)
        pp(TRUE)
        prompt("Bit 31")
    }
}
device(mbboDirect, CONSTANT, devMbboDirectSoft, "Soft Channel")
device(mbboDirect, CONSTANT, devMbboDirectSoftRaw, "Raw Soft Channel")
device(mbboDirect, CONSTANT, devMbboDirectSoftCallback, "Async Soft Channel")
device(mbboDirect, INST_IO, asynMbboDirectUInt32Digital, "asynUInt32Digital")
recordtype(longout) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct longoutRecord;
    %typedef struct longoutdset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*write_longout)(struct longoutRecord *prec); /*(-1,0)=>(failure,success*/
    %} longoutdset;
    %#define HAS_longoutdset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        promptgroup("50 - Output")
        asl(ASL0)
        pp(TRUE)
        prompt("Desired Output")
    }
    field(OUT, DBF_OUTLINK) {
        promptgroup("50 - Output")
        interest(1)
        prompt("Output Specification")
    }
    field(DOL, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Desired Output Loc")
    }
    field(OMSL, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuOmsl)
        interest(1)
        prompt("Output Mode Select")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(DRVH, DBF_LONG) {
        prop(YES)
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Drive High Limit")
    }
    field(DRVL, DBF_LONG) {
        prop(YES)
        promptgroup("30 - Action")
        interest(1)
        pp(TRUE)
        prompt("Drive Low Limit")
    }
    field(HOPR, DBF_LONG) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_LONG) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_LONG) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_LONG) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_LONG) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_LONG) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(LALM, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(SIOL, DBF_OUTLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Sim Output Specifctn")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Sim Mode Location")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Sim mode Alarm Svrty")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
    field(IVOA, DBF_MENU) {
        promptgroup("50 - Output")
        menu(menuIvoa)
        interest(2)
        prompt("INVALID output action")
    }
    field(IVOV, DBF_LONG) {
        promptgroup("50 - Output")
        interest(2)
        prompt("INVALID output value")
    }
}
device(longout, CONSTANT, devLoSoft, "Soft Channel")
device(longout, CONSTANT, devLoSoftCallback, "Async Soft Channel")
device(longout, INST_IO, asynLoInt32, "asynInt32")
device(longout, INST_IO, asynLoUInt32Digital, "asynUInt32Digital")
device(longout, INST_IO, asynLoInt64, "asynInt64")
device(longout, CAMAC_IO, devLongoutFru, "FRUinfo")
recordtype(aSub) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %struct aSubRecord;
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_LONG) {
        asl(ASL0)
        prompt("Subr. return value")
    }
    field(OVAL, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Old return value")
    }
    field(INAM, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        interest(1)
        size(41)
        prompt("Initialize Subr. Name")
    }
    field(LFLG, DBF_MENU) {
        promptgroup("30 - Action")
        menu(aSubLFLG)
        interest(1)
        prompt("Subr. Input Enable")
    }
    field(SUBL, DBF_INLINK) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        interest(1)
        prompt("Subroutine Name Link")
    }
    field(SNAM, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_MOD)
        interest(1)
        size(41)
        prompt("Process Subr. Name")
    }
    field(ONAM, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        interest(3)
        size(41)
        prompt("Old Subr. Name")
    }
    field(SADR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("long (*sadr)(struct aSubRecord *)")
        interest(2)
        prompt("Subroutine Address")
    }
    field(CADR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void (*cadr)(struct aSubRecord *)")
        interest(2)
        prompt("Subroutine Cleanup Address")
    }
    field(BRSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Bad Return Severity")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(EFLG, DBF_MENU) {
        promptgroup("50 - Output")
        menu(aSubEFLG)
        initial("1")
        interest(1)
        prompt("Output Event Flag")
    }
    field(INPA, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link A")
    }
    field(INPB, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link B")
    }
    field(INPC, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link C")
    }
    field(INPD, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link D")
    }
    field(INPE, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link E")
    }
    field(INPF, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link F")
    }
    field(INPG, DBF_INLINK) {
        promptgroup("41 - Input A-G")
        interest(1)
        prompt("Input Link G")
    }
    field(INPH, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link H")
    }
    field(INPI, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link I")
    }
    field(INPJ, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link J")
    }
    field(INPK, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link K")
    }
    field(INPL, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link L")
    }
    field(INPM, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link M")
    }
    field(INPN, DBF_INLINK) {
        promptgroup("42 - Input H-N")
        interest(1)
        prompt("Input Link N")
    }
    field(INPO, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link O")
    }
    field(INPP, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link P")
    }
    field(INPQ, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link Q")
    }
    field(INPR, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link R")
    }
    field(INPS, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link S")
    }
    field(INPT, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link T")
    }
    field(INPU, DBF_INLINK) {
        promptgroup("43 - Input O-U")
        interest(1)
        prompt("Input Link U")
    }
    field(A, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *a")
        interest(2)
        prompt("Input value A")
    }
    field(B, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *b")
        interest(2)
        prompt("Input value B")
    }
    field(C, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *c")
        interest(2)
        prompt("Input value C")
    }
    field(D, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *d")
        interest(2)
        prompt("Input value D")
    }
    field(E, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *e")
        interest(2)
        prompt("Input value E")
    }
    field(F, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *f")
        interest(2)
        prompt("Input value F")
    }
    field(G, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *g")
        interest(2)
        prompt("Input value G")
    }
    field(H, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *h")
        interest(2)
        prompt("Input value H")
    }
    field(I, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *i")
        interest(2)
        prompt("Input value I")
    }
    field(J, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *j")
        interest(2)
        prompt("Input value J")
    }
    field(K, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *k")
        interest(2)
        prompt("Input value K")
    }
    field(L, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *l")
        interest(2)
        prompt("Input value L")
    }
    field(M, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *m")
        interest(2)
        prompt("Input value M")
    }
    field(N, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *n")
        interest(2)
        prompt("Input value N")
    }
    field(O, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *o")
        interest(2)
        prompt("Input value O")
    }
    field(P, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *p")
        interest(2)
        prompt("Input value P")
    }
    field(Q, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *q")
        interest(2)
        prompt("Input value Q")
    }
    field(R, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *r")
        interest(2)
        prompt("Input value R")
    }
    field(S, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *s")
        interest(2)
        prompt("Input value S")
    }
    field(T, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *t")
        interest(2)
        prompt("Input value T")
    }
    field(U, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *u")
        interest(2)
        prompt("Input value U")
    }
    field(FTA, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of A")
    }
    field(FTB, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of B")
    }
    field(FTC, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of C")
    }
    field(FTD, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of D")
    }
    field(FTE, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of E")
    }
    field(FTF, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of F")
    }
    field(FTG, DBF_MENU) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of G")
    }
    field(FTH, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of H")
    }
    field(FTI, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of I")
    }
    field(FTJ, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of J")
    }
    field(FTK, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of K")
    }
    field(FTL, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of L")
    }
    field(FTM, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of M")
    }
    field(FTN, DBF_MENU) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of N")
    }
    field(FTO, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of O")
    }
    field(FTP, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of P")
    }
    field(FTQ, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of Q")
    }
    field(FTR, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of R")
    }
    field(FTS, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of S")
    }
    field(FTT, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of T")
    }
    field(FTU, DBF_MENU) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of U")
    }
    field(NOA, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in A")
    }
    field(NOB, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in B")
    }
    field(NOC, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in C")
    }
    field(NOD, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in D")
    }
    field(NOE, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in E")
    }
    field(NOF, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in F")
    }
    field(NOG, DBF_ULONG) {
        promptgroup("41 - Input A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in G")
    }
    field(NOH, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in H")
    }
    field(NOI, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in I")
    }
    field(NOJ, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in J")
    }
    field(NOK, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in K")
    }
    field(NOL, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in L")
    }
    field(NOM, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in M")
    }
    field(NON, DBF_ULONG) {
        promptgroup("42 - Input H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in N")
    }
    field(NOO, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in O")
    }
    field(NOP, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in P")
    }
    field(NOQ, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in Q")
    }
    field(NOR, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in R")
    }
    field(NOS, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in S")
    }
    field(NOT, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in T")
    }
    field(NOU, DBF_ULONG) {
        promptgroup("43 - Input O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in U")
    }
    field(NEA, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in A")
    }
    field(NEB, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in B")
    }
    field(NEC, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in C")
    }
    field(NED, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in D")
    }
    field(NEE, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in E")
    }
    field(NEF, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in F")
    }
    field(NEG, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in G")
    }
    field(NEH, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in H")
    }
    field(NEI, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in I")
    }
    field(NEJ, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in J")
    }
    field(NEK, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in K")
    }
    field(NEL, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in L")
    }
    field(NEM, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in M")
    }
    field(NEN, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in N")
    }
    field(NEO, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in O")
    }
    field(NEP, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in P")
    }
    field(NEQ, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in Q")
    }
    field(NER, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in R")
    }
    field(NES, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in S")
    }
    field(NET, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in T")
    }
    field(NEU, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in U")
    }
    field(OUTA, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link A")
    }
    field(OUTB, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link B")
    }
    field(OUTC, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link C")
    }
    field(OUTD, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link D")
    }
    field(OUTE, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link E")
    }
    field(OUTF, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link F")
    }
    field(OUTG, DBF_OUTLINK) {
        promptgroup("51 - Output A-G")
        interest(1)
        prompt("Output Link G")
    }
    field(OUTH, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link H")
    }
    field(OUTI, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link I")
    }
    field(OUTJ, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link J")
    }
    field(OUTK, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link K")
    }
    field(OUTL, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link L")
    }
    field(OUTM, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link M")
    }
    field(OUTN, DBF_OUTLINK) {
        promptgroup("52 - Output H-N")
        interest(1)
        prompt("Output Link N")
    }
    field(OUTO, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link O")
    }
    field(OUTP, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link P")
    }
    field(OUTQ, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link Q")
    }
    field(OUTR, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link R")
    }
    field(OUTS, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link S")
    }
    field(OUTT, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link T")
    }
    field(OUTU, DBF_OUTLINK) {
        promptgroup("53 - Output O-U")
        interest(1)
        prompt("Output Link U")
    }
    field(VALA, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *vala")
        interest(2)
        prompt("Output value A")
    }
    field(VALB, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valb")
        interest(2)
        prompt("Output value B")
    }
    field(VALC, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valc")
        interest(2)
        prompt("Output value C")
    }
    field(VALD, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *vald")
        interest(2)
        prompt("Output value D")
    }
    field(VALE, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *vale")
        interest(2)
        prompt("Output value E")
    }
    field(VALF, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valf")
        interest(2)
        prompt("Output value F")
    }
    field(VALG, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valg")
        interest(2)
        prompt("Output value G")
    }
    field(VALH, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valh")
        interest(2)
        prompt("Output value H")
    }
    field(VALI, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *vali")
        interest(2)
        prompt("Output value I")
    }
    field(VALJ, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valj")
        interest(2)
        prompt("Output value J")
    }
    field(VALK, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valk")
        interest(2)
        prompt("Output value K")
    }
    field(VALL, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *vall")
        interest(2)
        prompt("Output value L")
    }
    field(VALM, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valm")
        interest(2)
        prompt("Output value M")
    }
    field(VALN, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valn")
        interest(2)
        prompt("Output value N")
    }
    field(VALO, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valo")
        interest(2)
        prompt("Output value O")
    }
    field(VALP, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valp")
        interest(2)
        prompt("Output value P")
    }
    field(VALQ, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valq")
        interest(2)
        prompt("Output value Q")
    }
    field(VALR, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valr")
        interest(2)
        prompt("Output value R")
    }
    field(VALS, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *vals")
        interest(2)
        prompt("Output value S")
    }
    field(VALT, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valt")
        interest(2)
        prompt("Output value T")
    }
    field(VALU, DBF_NOACCESS) {
        special(SPC_DBADDR)
        asl(ASL0)
        extra("void *valu")
        interest(2)
        prompt("Output value U")
    }
    field(OVLA, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovla")
        interest(4)
        prompt("Old Output A")
    }
    field(OVLB, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlb")
        interest(4)
        prompt("Old Output B")
    }
    field(OVLC, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlc")
        interest(4)
        prompt("Old Output C")
    }
    field(OVLD, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovld")
        interest(4)
        prompt("Old Output D")
    }
    field(OVLE, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovle")
        interest(4)
        prompt("Old Output E")
    }
    field(OVLF, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlf")
        interest(4)
        prompt("Old Output F")
    }
    field(OVLG, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlg")
        interest(4)
        prompt("Old Output G")
    }
    field(OVLH, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlh")
        interest(4)
        prompt("Old Output H")
    }
    field(OVLI, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovli")
        interest(4)
        prompt("Old Output I")
    }
    field(OVLJ, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlj")
        interest(4)
        prompt("Old Output J")
    }
    field(OVLK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlk")
        interest(4)
        prompt("Old Output K")
    }
    field(OVLL, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovll")
        interest(4)
        prompt("Old Output L")
    }
    field(OVLM, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlm")
        interest(4)
        prompt("Old Output M")
    }
    field(OVLN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovln")
        interest(4)
        prompt("Old Output N")
    }
    field(OVLO, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlo")
        interest(4)
        prompt("Old Output O")
    }
    field(OVLP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlp")
        interest(4)
        prompt("Old Output P")
    }
    field(OVLQ, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlq")
        interest(4)
        prompt("Old Output Q")
    }
    field(OVLR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlr")
        interest(4)
        prompt("Old Output R")
    }
    field(OVLS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovls")
        interest(4)
        prompt("Old Output S")
    }
    field(OVLT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlt")
        interest(4)
        prompt("Old Output T")
    }
    field(OVLU, DBF_NOACCESS) {
        special(SPC_NOMOD)
        asl(ASL0)
        extra("void *ovlu")
        interest(4)
        prompt("Old Output U")
    }
    field(FTVA, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALA")
    }
    field(FTVB, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALB")
    }
    field(FTVC, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALC")
    }
    field(FTVD, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALD")
    }
    field(FTVE, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALE")
    }
    field(FTVF, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALF")
    }
    field(FTVG, DBF_MENU) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALG")
    }
    field(FTVH, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALH")
    }
    field(FTVI, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALI")
    }
    field(FTVJ, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALJ")
    }
    field(FTVK, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALK")
    }
    field(FTVL, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALL")
    }
    field(FTVM, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALM")
    }
    field(FTVN, DBF_MENU) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALN")
    }
    field(FTVO, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALO")
    }
    field(FTVP, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALP")
    }
    field(FTVQ, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALQ")
    }
    field(FTVR, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALR")
    }
    field(FTVS, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALS")
    }
    field(FTVT, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALT")
    }
    field(FTVU, DBF_MENU) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        menu(menuFtype)
        initial("DOUBLE")
        interest(1)
        prompt("Type of VALU")
    }
    field(NOVA, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALA")
    }
    field(NOVB, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALB")
    }
    field(NOVC, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALC")
    }
    field(NOVD, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALD")
    }
    field(NOVE, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALE")
    }
    field(NOVF, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALF")
    }
    field(NOVG, DBF_ULONG) {
        promptgroup("51 - Output A-G")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALG")
    }
    field(NOVH, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VAlH")
    }
    field(NOVI, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALI")
    }
    field(NOVJ, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALJ")
    }
    field(NOVK, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALK")
    }
    field(NOVL, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALL")
    }
    field(NOVM, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALM")
    }
    field(NOVN, DBF_ULONG) {
        promptgroup("52 - Output H-N")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALN")
    }
    field(NOVO, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALO")
    }
    field(NOVP, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALP")
    }
    field(NOVQ, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALQ")
    }
    field(NOVR, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALR")
    }
    field(NOVS, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALS")
    }
    field(NOVT, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALT")
    }
    field(NOVU, DBF_ULONG) {
        promptgroup("53 - Output O-U")
        special(SPC_NOMOD)
        initial("1")
        interest(1)
        prompt("Max. elements in VALU")
    }
    field(NEVA, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALA")
    }
    field(NEVB, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALB")
    }
    field(NEVC, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALC")
    }
    field(NEVD, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALD")
    }
    field(NEVE, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALE")
    }
    field(NEVF, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALF")
    }
    field(NEVG, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALG")
    }
    field(NEVH, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VAlH")
    }
    field(NEVI, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALI")
    }
    field(NEVJ, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALJ")
    }
    field(NEVK, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALK")
    }
    field(NEVL, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALL")
    }
    field(NEVM, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALM")
    }
    field(NEVN, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALN")
    }
    field(NEVO, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALO")
    }
    field(NEVP, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALP")
    }
    field(NEVQ, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALQ")
    }
    field(NEVR, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALR")
    }
    field(NEVS, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALS")
    }
    field(NEVT, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALT")
    }
    field(NEVU, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(3)
        prompt("Num. elements in VALU")
    }
    field(ONVA, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLA")
    }
    field(ONVB, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLB")
    }
    field(ONVC, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLC")
    }
    field(ONVD, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLD")
    }
    field(ONVE, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLE")
    }
    field(ONVF, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLF")
    }
    field(ONVG, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLG")
    }
    field(ONVH, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in VAlH")
    }
    field(ONVI, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLI")
    }
    field(ONVJ, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLJ")
    }
    field(ONVK, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLK")
    }
    field(ONVL, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLL")
    }
    field(ONVM, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLM")
    }
    field(ONVN, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLN")
    }
    field(ONVO, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLO")
    }
    field(ONVP, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLP")
    }
    field(ONVQ, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLQ")
    }
    field(ONVR, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLR")
    }
    field(ONVS, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLS")
    }
    field(ONVT, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLT")
    }
    field(ONVU, DBF_ULONG) {
        special(SPC_NOMOD)
        initial("1")
        interest(4)
        prompt("Num. elements in OVLU")
    }
}
recordtype(sub) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %struct subRecord;
    %typedef long (*SUBFUNCPTR)(struct subRecord *);
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_DOUBLE) {
        asl(ASL0)
        pp(TRUE)
        prompt("Result")
    }
    field(INAM, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_NOMOD)
        interest(1)
        size(40)
        prompt("Init Routine Name")
    }
    field(SNAM, DBF_STRING) {
        promptgroup("30 - Action")
        special(SPC_MOD)
        interest(1)
        size(40)
        prompt("Subroutine Name")
    }
    field(SADR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("SUBFUNCPTR sadr")
        interest(4)
        prompt("Subroutine Address")
    }
    field(INPA, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input A")
    }
    field(INPB, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input B")
    }
    field(INPC, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input C")
    }
    field(INPD, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input D")
    }
    field(INPE, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input E")
    }
    field(INPF, DBF_INLINK) {
        promptgroup("41 - Input A-F")
        interest(1)
        prompt("Input F")
    }
    field(INPG, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input G")
    }
    field(INPH, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input H")
    }
    field(INPI, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input I")
    }
    field(INPJ, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input J")
    }
    field(INPK, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input K")
    }
    field(INPL, DBF_INLINK) {
        promptgroup("42 - Input G-L")
        interest(1)
        prompt("Input L")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(BRSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Bad Return Severity")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(A, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input A")
    }
    field(B, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input B")
    }
    field(C, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input C")
    }
    field(D, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input D")
    }
    field(E, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input E")
    }
    field(F, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input F")
    }
    field(G, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input G")
    }
    field(H, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input H")
    }
    field(I, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input I")
    }
    field(J, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input J")
    }
    field(K, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input K")
    }
    field(L, DBF_DOUBLE) {
        pp(TRUE)
        prompt("Value of Input L")
    }
    field(LA, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of A")
    }
    field(LB, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of B")
    }
    field(LC, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of C")
    }
    field(LD, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of D")
    }
    field(LE, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of E")
    }
    field(LF, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of F")
    }
    field(LG, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of G")
    }
    field(LH, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of H")
    }
    field(LI, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of I")
    }
    field(LJ, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of J")
    }
    field(LK, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of K")
    }
    field(LL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Prev Value of L")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Monitored")
    }
}
recordtype(int64in) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct int64inRecord;
    %typedef struct int64indset {
    %    dset common;
    %    long (*read_int64in)(struct int64inRecord *prec);
    %} int64indset;
    %#define HAS_int64indset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_INT64) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Current value")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Units name")
    }
    field(HOPR, DBF_INT64) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_INT64) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(HIHI, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_INT64) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_INT64) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(AFTC, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Filter Time Constant")
    }
    field(AFVL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Alarm Filter Value")
    }
    field(ADEL, DBF_INT64) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_INT64) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(LALM, DBF_INT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(ALST, DBF_INT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_INT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_INT64) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(int64in, CONSTANT, devI64inSoft, "Soft Channel")
device(int64in, CONSTANT, devI64inSoftCallback, "Async Soft Channel")
device(int64in, INST_IO, asynInt64In, "asynInt64")
recordtype(ai) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct aiRecord;
    %typedef struct aidset {
    %    dset common;
    %    long (*read_ai)(struct aiRecord *prec);
    %    long (*special_linconv)(struct aiRecord *prec, int after);
    %} aidset;
    %#define HAS_aidset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_DOUBLE) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        prompt("Current EGU Value")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(PREC, DBF_SHORT) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Display Precision")
    }
    field(LINR, DBF_MENU) {
        promptgroup("60 - Convert")
        special(SPC_LINCONV)
        menu(menuConvert)
        interest(1)
        pp(TRUE)
        prompt("Linearization")
    }
    field(EGUF, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        special(SPC_LINCONV)
        interest(1)
        pp(TRUE)
        prompt("Engineer Units Full")
    }
    field(EGUL, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        special(SPC_LINCONV)
        interest(1)
        pp(TRUE)
        prompt("Engineer Units Low")
    }
    field(EGU, DBF_STRING) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        size(16)
        prompt("Engineering Units")
    }
    field(HOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("High Operating Range")
    }
    field(LOPR, DBF_DOUBLE) {
        prop(YES)
        promptgroup("80 - Display")
        interest(1)
        prompt("Low Operating Range")
    }
    field(AOFF, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        interest(1)
        pp(TRUE)
        prompt("Adjustment Offset")
    }
    field(ASLO, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Adjustment Slope")
    }
    field(SMOO, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        interest(1)
        prompt("Smoothing")
    }
    field(HIHI, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Hihi Alarm Limit")
    }
    field(LOLO, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Lolo Alarm Limit")
    }
    field(HIGH, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("High Alarm Limit")
    }
    field(LOW, DBF_DOUBLE) {
        prop(YES)
        promptgroup("70 - Alarm")
        interest(1)
        pp(TRUE)
        prompt("Low Alarm Limit")
    }
    field(HHSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Hihi Severity")
    }
    field(LLSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Lolo Severity")
    }
    field(HSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("High Severity")
    }
    field(LSV, DBF_MENU) {
        prop(YES)
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        pp(TRUE)
        prompt("Low Severity")
    }
    field(HYST, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Deadband")
    }
    field(AFTC, DBF_DOUBLE) {
        promptgroup("70 - Alarm")
        interest(1)
        prompt("Alarm Filter Time Constant")
    }
    field(ADEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Archive Deadband")
    }
    field(MDEL, DBF_DOUBLE) {
        promptgroup("80 - Display")
        interest(1)
        prompt("Monitor Deadband")
    }
    field(LALM, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Alarmed")
    }
    field(AFVL, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Alarm Filter Value")
    }
    field(ALST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Value Archived")
    }
    field(MLST, DBF_DOUBLE) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Last Val Monitored")
    }
    field(ESLO, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        initial("1")
        interest(2)
        pp(TRUE)
        prompt("Raw to EGU Slope")
    }
    field(EOFF, DBF_DOUBLE) {
        promptgroup("60 - Convert")
        interest(2)
        pp(TRUE)
        prompt("Raw to EGU Offset")
    }
    field(ROFF, DBF_ULONG) {
        interest(2)
        pp(TRUE)
        prompt("Raw Offset")
    }
    field(PBRK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void *   pbrk")
        interest(4)
        prompt("Ptrto brkTable")
    }
    field(INIT, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Initialized?")
    }
    field(LBRK, DBF_SHORT) {
        special(SPC_NOMOD)
        interest(3)
        prompt("LastBreak Point")
    }
    field(RVAL, DBF_LONG) {
        pp(TRUE)
        prompt("Current Raw Value")
    }
    field(ORAW, DBF_LONG) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Previous Raw Value")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_DOUBLE) {
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuSimm)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(ai, CONSTANT, devAiSoft, "Soft Channel")
device(ai, CONSTANT, devAiSoftRaw, "Raw Soft Channel")
device(ai, CONSTANT, devAiSoftCallback, "Async Soft Channel")
device(ai, INST_IO, devTimestampAI, "Soft Timestamp")
device(ai, INST_IO, devAiGeneralTime, "General Time")
device(ai, INST_IO, asynAiInt32, "asynInt32")
device(ai, INST_IO, asynAiInt32Average, "asynInt32Average")
device(ai, INST_IO, asynAiFloat64, "asynFloat64")
device(ai, INST_IO, asynAiFloat64Average, "asynFloat64Average")
device(ai, INST_IO, asynAiInt64, "asynInt64")
device(ai, INST_IO, devAiStats, "IOC stats")
device(ai, INST_IO, devAiClusts, "IOC stats clusts")
device(ai, CAMAC_IO, devAiMch, "MCHsensor")
device(ai, CAMAC_IO, devAiFru, "FRUinfo")
recordtype(stringin) {
    %#include "epicsTypes.h"
    %#include "link.h"
    %#include "epicsMutex.h"
    %#include "ellLib.h"
    %#include "devSup.h"
    %#include "epicsTime.h"
    %
    %/* Declare Device Support Entry Table */
    %struct stringinRecord;
    %typedef struct stringindset {
    %    dset common; /*init_record returns: (-1,0)=>(failure,success)*/
    %    long (*read_stringin)(struct stringinRecord *prec); /*returns: (-1,0)=>(failure,success)*/
    %} stringindset;
    %#define HAS_stringindset
    %
    %#include "callback.h"
    field(NAME, DBF_STRING) {
        special(SPC_NOMOD)
        size(61)
        prompt("Record Name")
    }
    field(DESC, DBF_STRING) {
        promptgroup("10 - Common")
        size(41)
        prompt("Descriptor")
    }
    field(ASG, DBF_STRING) {
        promptgroup("10 - Common")
        special(SPC_AS)
        size(29)
        prompt("Access Security Group")
    }
    field(SCAN, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuScan)
        interest(1)
        prompt("Scan Mechanism")
    }
    field(PINI, DBF_MENU) {
        promptgroup("20 - Scan")
        menu(menuPini)
        interest(1)
        prompt("Process at iocInit")
    }
    field(PHAS, DBF_SHORT) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        prompt("Scan Phase")
    }
    field(EVNT, DBF_STRING) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        interest(1)
        size(40)
        prompt("Event Name")
    }
    field(TSE, DBF_SHORT) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Event")
    }
    field(TSEL, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Time Stamp Link")
    }
    field(DTYP, DBF_DEVICE) {
        promptgroup("10 - Common")
        interest(1)
        prompt("Device Type")
    }
    field(DISV, DBF_SHORT) {
        promptgroup("20 - Scan")
        initial("1")
        prompt("Disable Value")
    }
    field(DISA, DBF_SHORT) {
        prompt("Disable")
    }
    field(SDIS, DBF_INLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Scanning Disable")
    }
    field(MLOK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsMutexId        mlok")
        interest(4)
        prompt("Monitor lock")
    }
    field(MLIS, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             mlis")
        interest(4)
        prompt("Monitor List")
    }
    field(BKLNK, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("ELLLIST             bklnk")
        interest(4)
        prompt("Backwards link tracking")
    }
    field(DISP, DBF_UCHAR) {
        prompt("Disable putField")
    }
    field(PROC, DBF_UCHAR) {
        interest(3)
        pp(TRUE)
        prompt("Force Processing")
    }
    field(STAT, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        initial("UDF")
        prompt("Alarm Status")
    }
    field(SEVR, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        prompt("Alarm Severity")
    }
    field(AMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("Alarm Message")
    }
    field(NSTA, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmStat)
        interest(2)
        prompt("New Alarm Status")
    }
    field(NSEV, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("New Alarm Severity")
    }
    field(NAMSG, DBF_STRING) {
        special(SPC_NOMOD)
        size(40)
        prompt("New Alarm Message")
    }
    field(ACKS, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuAlarmSevr)
        interest(2)
        prompt("Alarm Ack Severity")
    }
    field(ACKT, DBF_MENU) {
        promptgroup("70 - Alarm")
        special(SPC_NOMOD)
        menu(menuYesNo)
        initial("YES")
        interest(2)
        prompt("Alarm Ack Transient")
    }
    field(DISS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        interest(1)
        prompt("Disable Alarm Sevrty")
    }
    field(LCNT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(2)
        prompt("Lock Count")
    }
    field(PACT, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Record active")
    }
    field(PUTF, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("dbPutField process")
    }
    field(RPRO, DBF_UCHAR) {
        special(SPC_NOMOD)
        interest(1)
        prompt("Reprocess ")
    }
    field(ASP, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct asgMember    *asp")
        interest(4)
        prompt("Access Security Pvt")
    }
    field(PPN, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotify *ppn")
        interest(4)
        prompt("pprocessNotify")
    }
    field(PPNR, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct processNotifyRecord *ppnr")
        interest(4)
        prompt("pprocessNotifyRecord")
    }
    field(SPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct scan_element *spvt")
        interest(4)
        prompt("Scan Private")
    }
    field(RSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct typed_rset   *rset")
        interest(4)
        prompt("Address of RSET")
    }
    field(DSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("unambiguous_dset    *dset")
        interest(4)
        prompt("DSET address")
    }
    field(DPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("void                *dpvt")
        interest(4)
        prompt("Device Private")
    }
    field(RDES, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct dbRecordType *rdes")
        interest(4)
        prompt("Address of dbRecordType")
    }
    field(LSET, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("struct lockRecord   *lset")
        interest(4)
        prompt("Lock Set")
    }
    field(PRIO, DBF_MENU) {
        promptgroup("20 - Scan")
        special(SPC_SCAN)
        menu(menuPriority)
        interest(1)
        prompt("Scheduling Priority")
    }
    field(TPRO, DBF_UCHAR) {
        prompt("Trace Processing")
    }
    field(BKPT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsUInt8          bkpt")
        interest(1)
        prompt("Break Point")
    }
    field(UDF, DBF_UCHAR) {
        promptgroup("10 - Common")
        initial("1")
        interest(1)
        pp(TRUE)
        prompt("Undefined")
    }
    field(UDFS, DBF_MENU) {
        promptgroup("70 - Alarm")
        menu(menuAlarmSevr)
        initial("INVALID")
        interest(1)
        prompt("Undefined Alarm Sevrty")
    }
    field(TIME, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsTimeStamp      time")
        interest(2)
        prompt("Time")
    }
    field(UTAG, DBF_UINT64) {
        special(SPC_NOMOD)
        interest(3)
        prompt("Time Tag")
    }
    field(FLNK, DBF_FWDLINK) {
        promptgroup("20 - Scan")
        interest(1)
        prompt("Forward Process Link")
    }
    field(VAL, DBF_STRING) {
        promptgroup("40 - Input")
        asl(ASL0)
        pp(TRUE)
        size(40)
        prompt("Current Value")
    }
    field(OVAL, DBF_STRING) {
        special(SPC_NOMOD)
        interest(3)
        size(40)
        prompt("Previous Value")
    }
    field(INP, DBF_INLINK) {
        promptgroup("40 - Input")
        interest(1)
        prompt("Input Specification")
    }
    field(MPST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(stringinPOST)
        interest(1)
        prompt("Post Value Monitors")
    }
    field(APST, DBF_MENU) {
        promptgroup("80 - Display")
        menu(stringinPOST)
        interest(1)
        prompt("Post Archive Monitors")
    }
    field(SIOL, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Input Link")
    }
    field(SVAL, DBF_STRING) {
        pp(TRUE)
        size(40)
        prompt("Simulation Value")
    }
    field(SIML, DBF_INLINK) {
        promptgroup("90 - Simulate")
        interest(1)
        prompt("Simulation Mode Link")
    }
    field(SIMM, DBF_MENU) {
        special(SPC_MOD)
        menu(menuYesNo)
        interest(1)
        prompt("Simulation Mode")
    }
    field(SIMS, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuAlarmSevr)
        interest(2)
        prompt("Simulation Mode Severity")
    }
    field(OLDSIMM, DBF_MENU) {
        special(SPC_NOMOD)
        menu(menuSimm)
        interest(4)
        prompt("Prev. Simulation Mode")
    }
    field(SSCN, DBF_MENU) {
        promptgroup("90 - Simulate")
        menu(menuScan)
        initial("65535")
        interest(1)
        prompt("Sim. Mode Scan")
    }
    field(SDLY, DBF_DOUBLE) {
        promptgroup("90 - Simulate")
        initial("-1.0")
        interest(2)
        prompt("Sim. Mode Async Delay")
    }
    field(SIMPVT, DBF_NOACCESS) {
        special(SPC_NOMOD)
        extra("epicsCallback            *simpvt")
        interest(4)
        prompt("Sim. Mode Private")
    }
}
device(stringin, CONSTANT, devSiSoft, "Soft Channel")
device(stringin, CONSTANT, devSiSoftCallback, "Async Soft Channel")
device(stringin, INST_IO, devTimestampSI, "Soft Timestamp")
device(stringin, INST_IO, devSiGeneralTime, "General Time")
device(stringin, INST_IO, devSiEnviron, "getenv")
device(stringin, INST_IO, asynSiOctetCmdResponse, "asynOctetCmdResponse")
device(stringin, INST_IO, asynSiOctetWriteRead, "asynOctetWriteRead")
device(stringin, INST_IO, asynSiOctetRead, "asynOctetRead")
device(stringin, INST_IO, devStringinStats, "IOC stats")
device(stringin, INST_IO, devStringinEnvVar, "IOC env var")
device(stringin, INST_IO, devStringinEpics, "IOC epics var")
device(stringin, CAMAC_IO, devStringinFru, "FRUinfo")
driver(drvMch)
driver(drvAsyn)
link(state, lnkStateIf)
link(calc, lnkCalcIf)
link(trace, lnkTraceIf)
link(debug, lnkDebugIf)
link(const, lnkConstIf)
registrar(drvMchServerPcRegistrar)
registrar(tsInitialize)
registrar(drvMchPicmgRegistrar)
registrar(syncInitialize)
registrar(utagInitialize)
registrar(asynRegister)
registrar(decInitialize)
registrar(asynInterposeFlushRegister)
registrar(drvAsynIPPortRegisterCommands)
registrar(arrInitialize)
registrar(asynInterposeEchoRegister)
registrar(asynInterposeDelayRegister)
registrar(drvMchRegisterCommands)
registrar(dbndInitialize)
registrar(drvAsynIPServerPortRegisterCommands)
registrar(asSub)
registrar(rsrvRegistrar)
registrar(asynInterposeEosRegister)
function(subMchTypeFacility)
function(scanMon)
function(rebootProc)
function(scanMonInit)
variable(dbTemplateMaxVars, int)
variable(lnkDebug_debug, int)
variable(asCaDebug, int)
variable(callbackParallelThreadsDefault, int)
variable(dbAccessDebugPUTF, int)
variable(dbRecordsOnceOnly, int)
variable(calcoutODLYlimit, double)
variable(dbBptNotMonotonic, int)
variable(atExitDebug, int)
variable(boHIGHlimit, double)
variable(dbJLinkDebug, int)
variable(dbConvertStrict, int)
variable(seqDLYprecision, int)
variable(logClientDebug, int)
variable(dbQuietMacroWarnings, int)
variable(boHIGHprecision, int)
variable(dbRecordsAbcSorted, int)
variable(seqDLYlimit, double)
variable(calcoutODLYprecision, int)
variable(histogramSDELprecision, int)
variable(dbThreadRealtimeLock, int)
variable(CASDEBUG, int)
