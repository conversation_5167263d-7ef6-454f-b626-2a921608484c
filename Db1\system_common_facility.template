record(aSub, "$(dev):TYPE_SUB") {
  field(DESC, "Determine LCLS device type")
  field(SNAM, "subMchTypeFacility")
  field(PINI, "YES")
  field(FTA,  "DOUBLE")
  field(INPA, "$(dev):TYPE_RAW MS CP")
# B-H are 'type' values to be used by
# facility-specific software (in particular
# LCLS IOCManager)
# B is type value for Unknown device
  field(FTB,  "DOUBLE")
  field(INPB, "$(unknown)")
# C is type value for Vadatech MCH
  field(FTC,  "DOUBLE")
  field(INPC, "$(vadatech)")
# D is type value for NAT MCH
  field(FTD,  "DOUBLE")
  field(INPD, "$(nat)")
# E is type value for Supermicro server
  field(FTE,  "DOUBLE")
  field(INPE, "$(supermicro)")
# F is type value for Pentair shelf manager
  field(FTF,  "DOUBLE")
  field(INPF, "$(pentair)")
# G is type value for Artesyn shelf manager
  field(FTG,  "DOUBLE")
  field(INPG, "$(artesyn)")
# H is type value for Advantech server
  field(FTH,  "DOUBLE")
  field(INPH, "$(advantech)")
# VALA is 'type' value used in LCLS IOCManager
  field(FTVA, "DOUBLE")
  field(FLNK, "$(dev):TYPE_DESC")
}

# Copy description to TYPE.DESC
record(stringout, "$(dev):TYPE_DESC") {
  field(OMSL, "closed_loop")
  field(DOL,  "$(dev):TYPE_RAW")
  field(OUT,  "$(dev):TYPE.DESC")
  field(FLNK, "$(dev):TYPE")
}

# DESC filled in by TYPE_DESC PV
record(ai, "$(dev):TYPE") {
  field(DESC, "")
  field(INP, "$(dev):TYPE_SUB.VALA")
}
