record(mbbi, "$(dev):TYPE_RAW") {
 field(DESC, "Device type")
 field(DTYP, "MCHsensor")
 field(SCAN, "I/O Intr")
 field(INP, "#B0 C0 N0 @$(link)+mch")
 field(ZRST, "Unknown")
 field(ZRVL, "0")
 field(ZRSV, "MAJOR")
 field(ONST, "MicroTCA")
 field(ONVL, "1")
 field(ONSV, "NO_ALARM")
 field(TWST, "MicroTCA")
 field(TWVL, "2")
 field(TWSV, "NO_ALARM")
 field(THST, "Supermicro")
 field(THVL, "3")
 field(THSV, "NO_ALARM")
 field(FRST, "ATCA")
 field(FRVL, "4")
 field(FRSV, "NO_ALARM")
 field(FVVL, "5")
 field(FVST, "ATCA")
 field(FVSV, "NO_ALARM")
 field(SXVL, "6")
 field(SXST, "Advantech")
 field(SXSV, "NO_ALARM")
 field(PINI, "YES")
}

record(stringin, "$(dev):LOC") {
  field(PINI, "YES")
  field(VAL, "$(location)")
  info(autosaveFields,"VAL")
}

# Control debug message verbosity
record(mbbo, "$(dev):DBG") {
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(ZRVL, "0")
 field(ZRST, "Off")
 field(ONVL, "1")
 field(ONST, "Low")
 field(TWVL, "2")
 field(TWST, "Med")
 field(THVL, "3")
 field(THST, "High")
 field(OUT,  "#B0 C0 N0 @$(link)+dbg")
 field(VAL,  "0")
 field(PINI, "YES")
}

record(bi, "$(dev):ONLNSTAT") {
 field(DESC, "MCH online status")
 field(DTYP, "MCHsensor")
 field(SCAN, "I/O Intr")
 field(INP, "#B0 C0 N0 @$(link)+stat")
 field(ZNAM, "Offline")
 field(ZSV, "MAJOR")
 field(ONAM, "Online")
 field(OSV, "NO_ALARM")
 field(PINI, "YES")
 alias("$(dev):STAT")
}

record(mbbi, "$(dev):INIT") {
 field(DESC, "MCH comm initialized")
 field(DTYP, "MCHsensor")
 field(SCAN, "I/O Intr")
 field(INP, "#B0 C0 N0 @$(link)+init")
 field(ZRST, "Not initialized")
 field(ZRVL, "0")
 field(ZRSV, "MAJOR")
 field(ONST, "Initializing...")
 field(ONVL, "1")
 field(ONSV, "MINOR")
 field(TWST, "Initialized")
 field(TWVL, "2")
 field(TWSV, "NO_ALARM")
 field(THST, "Initialize failed")
 field(THVL, "3")
 field(THSV, "MAJOR")
 field(PINI, "YES")
}

record(bo, "$(dev):INITBYP") {
 field(DESC, "Override init for testing")
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(OUT, "#B0 C0 N0 @$(link)+init")
 field(ZNAM, "Not initialized")
 field(ZSV, "MAJOR")
 field(ONAM, "Initialized")
 field(OSV, "NO_ALARM")
 field(PINI, "NO")
}

record(bo, "$(dev):CONNECT") {
 field(DESC, "Enable comm with MCH")
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(OUT,  "#B0 C0 N0 @$(link)+sess")
 field(ZNAM, "Disconnect")
 field(ZSV,  "MAJOR")
 field(ONAM, "Connect")
 field(OSV,  "NO_ALARM")
 field(VAL, "1")
 field(RVAL, "1")
 field(PINI, "YES") 
 info(autosaveFields,"VAL RVAL")
}

# Add power on, power off, hard-reset, and msg
# PVs to be consistent with other SLAC systems       
record(bo, "$(dev):POWEROFF") {     
  field(DESC, "Power off system")            
  field(FLNK, "$(dev):POWEROFFSEQ")
  field(PINI, "NO")             
}                                               

record(seq, "$(dev):POWEROFFSEQ") {
  field(SELM, "All")               
  field(DOL1, "1")                 
  field(LNK1, "$(dev):POWERMSG PP")
  field(DOL2, "0")                 
  field(LNK2, "$(dev):POWERCTL PP")
  field(DLY3, "5")                
  field(DOL3, "0")                
  field(LNK3, "$(dev):POWERMSG PP")
}                                   

record(bo, "$(dev):POWERON") {
  field(DESC, "Power on system")            
  field(FLNK, "$(dev):POWERONSEQ")
  field(PINI, "NO")             
}                                 

record(seq, "$(dev):POWERONSEQ") {
  field(SELM, "All")              
  field(DOL1, "2")                 
  field(LNK1, "$(dev):POWERMSG PP")
  field(DOL2, "1")                
  field(LNK2, "$(dev):POWERCTL PP")
  field(DLY3, "5")                
  field(DOL3, "0")                
  field(LNK3, "$(dev):POWERMSG PP")
}                                   

record(bo, "$(dev):HARDRESET") {
  field(DESC, "Power cycle system")            
  field(FLNK, "$(dev):HARDRESETSEQ")
  field(PINI, "NO")
}                                   

record(seq, "$(dev):HARDRESETSEQ") {
  field(SELM, "All")                
  field(DOL1, "3")                 
  field(LNK1, "$(dev):POWERMSG PP")
  field(DOL2, "2")                  
  field(LNK2, "$(dev):POWERCTL PP") 
  field(DLY3, "5")                 
  field(DOL3, "0")                 
  field(LNK3, "$(dev):POWERMSG PP")
}                                   

record(bo, "$(dev):SOFTSHUTDOWN") {     
  field(DESC, "Soft shutdown system")            
  field(FLNK, "$(dev):SOFTSHUTDOWNSEQ")
  field(PINI, "NO")             
}                                               

record(seq, "$(dev):SOFTSHUTDOWNSEQ") {
  field(SELM, "All")               
  field(DOL1, "1")                 
  field(LNK1, "$(dev):POWERMSG PP")
  field(DOL2, "5")                 
  field(LNK2, "$(dev):POWERCTL PP")
  field(DLY3, "5")                
  field(DOL3, "0")                
  field(LNK3, "$(dev):POWERMSG PP")
}                                   

record(mbbo, "$(dev):POWERMSG") {
  field(ZRVL, "0")               
  field(ONVL, "1")               
  field(TWVL, "2")               
  field(THVL, "3")               
  field(ONST, "TURNING_OFF")     
  field(TWST, "TURNING_ON")      
  field(THST, "RESETTING")       
  field(ZRSV, "NO_ALARM")        
  field(ONSV, "MINOR")           
  field(TWSV, "MINOR")           
  field(THSV, "MINOR")           
  field(VAL, "0")                
  field(PINI, "YES")             
}                                

# Sends 'Chassis Power' IPMI command
# Vadatech MCH results are unreliable
# NAT MCH does not implement this command
# Dell Server and ATCA crates do seem to implement this
record(mbbo, "$(dev):POWERCTL") {
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(OUT, "#B0 C0 N0 @$(link)+chas")
 field(ZRVL, "0")
 field(ZRST, "Power off")
 field(ONVL, "1")
 field(ONST, "Power on")
 field(THVL, "3")
 field(THST, "Hard reset")
# Soft shutdown required by PICMG 3.0
# Tested in ATCA, but not MicroTCA
 field(FVVL, "5")
 field(FVST, "Soft shutdown")
 field(VAL, "1")
}

# Peform IPMI 'Cold Reset'
record(bo, "$(dev):RESET") {
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(OUT, "#B0 C0 N0 @$(link)+reset")
}

# Control sensor scan period [seconds]
# Default is 10 seconds
# Added this because PAL systems had 
# trouble at that rate
# Must match SENSOR_SCAN_PERIODS array 
# definition in devMch.c
record(mbbo, "$(dev):SENSOR_SCAN_PERIOD") {
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(ZRVL, "0")
 field(ZRST, "5 seconds")
 field(ONVL, "1")
 field(ONST, "10 seconds")
 field(TWVL, "2")
 field(TWST, "20 seconds")
 field(THVL, "3")
 field(THST, "30 seconds")
 field(FRVL, "4")
 field(FRST, "60 seconds")
 field(OUT,  "#B0 C0 N0 @$(link)+scan")
 field(VAL,  "1")
 field(RVAL, "1")
 field(PINI, "YES")
 info(autosaveFields,"VAL RVAL")
}
# 24 bits assigned as follows:
# 0-7   power state byte (bit 7 ignored)
# 8-15  last power event byte (bits 5-7 ignored)
# 16-23 misc chassis state byte (bits 4-7 ignored)
record(longin, "$(dev):CHASSTAT") {
 field(DESC, "Chassis status")
 field(DTYP, "MCHsensor")
 field(SCAN, "5 second")
 field(INP, "#B0 C0 N0 @$(link)+chas")
 field(FLNK, "$(dev):POWERINFO_CALC")
}

record(calc, "$(dev):POWERINFO_CALC") {
 field(DESC, "Chassis power info")
 field(CALC, "A & 0x7F")
 field(INPA, "$(dev):CHASSTAT MS")
 field(FLNK, "$(dev):POWERSTAT_CALC")
}

record(calc, "$(dev):POWERSTAT_CALC") {
 field(DESC, "Chassis power status")
 field(CALC, "A & 0x1F")
 field(INPA, "$(dev):POWERINFO_CALC MS")
 field(FLNK, "$(dev):POWERSTAT")
}

record(longin,"$(dev):POWERSTAT") {
  field(DESC, "Chassis power status")
  field(INP,  "$(dev):POWERSTAT_CALC MS")
  field(FLNK, "$(dev):POWERSTATE_CALC")
}

# Name POWERSTATE to be consistent with other 
# SLAC systems
record(calc,  "$(dev):POWERSTATE_CALC") {
  field(DESC, "Power on state")
  field(CALC, "A & 0x1")
  field(INPA, "$(dev):POWERSTAT MS")
  field(FLNK, "$(dev):POWERSTATE")
}

record(bi,    "$(dev):POWERSTATE") {
  field(DESC, "Power on state")
  field(INP,  "$(dev):POWERSTATE_CALC MS")
  field(ZNAM, "Not on")
  field(ZSV,  "MAJOR")
  field(ONAM, "On")
  field(OSV,  "NO_ALARM")
  field(FLNK, "$(dev):POWERPOLICY_CALC")
}

record(calc, "$(dev):POWERPOLICY_CALC") {
 field(DESC, "Chassis power policy")
 field(CALC, "(A & 0x60)>>5")
 field(INPA, "$(dev):POWERINFO_CALC MS")
 field(FLNK, "$(dev):POWERPOLICY")
}

record(mbbi,  "$(dev):POWERPOLICY") {
  field(DESC, "Chassis power policy")
  field(INP,  "$(dev):POWERPOLICY_CALC")
  field(ZRST, "Remains off")
  field(ZRVL, "0")
  field(ONST, "Previous state")
  field(ONVL, "1")
  field(TWST, "Powers up")
  field(TWVL, "2")
  field(THST, "Unknown")
  field(THVL, "3")
  field(FLNK, "$(dev):MISCSTAT_CALC")
}

record(calc, "$(dev):MISCSTAT_CALC") {
 field(DESC, "Chassis misc status")
 field(CALC, "(A & 0x0F0000)>>16")
 field(INPA, "$(dev):CHASSTAT MS")
 field(FLNK, "$(dev):MISCSTAT")
}

record(longin, "$(dev):MISCSTAT") {
 field(DESC, "Chassis misc status")
 field(INP,  "$(dev):MISCSTAT_CALC MS")
 field(FLNK, "$(dev):LASTPOWER_CALC")
}

record(calc, "$(dev):LASTPOWER_CALC") {
 field(DESC, "Last power event")
 field(CALC, "(A & 0x1F00)>>8")
 field(INPA, "$(dev):CHASSTAT MS")
 field(FLNK, "$(dev):LASTPOWER")
}

record(mbbi, "$(dev):LASTPOWER") {
 field(DESC, "Last power event")
 field(INP,  "$(dev):LASTPOWER_CALC MS")
 field(ZRST, "N/A")
 field(ZRVL, "0")
 field(ONST, "AC power failed")
 field(ONVL, "1")
 field(TWST, "Power overload")
 field(TWVL, "2")
 field(THST, "Power interlock")
 field(THVL, "4")
 field(FRST, "Power fault")
 field(FRVL, "8")
 field(FVST, "Remote power on")
 field(FVVL, "32") 
}
# Aliases are temporary for backward-compatibility

# FRU 'presence' record to indicate whether FRU
# is active; used by edm visibility rules
record(bi, "$(dev):FRU0:P") {
  field(DESC, "Module presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C0 N0 @$(link)+fpres")
  field(ZNAM, "Not present")
  field(ONAM, "Present")
  field(PINI, "YES")
  alias("$(dev):FRU0P")
}

# Vadatech 'hotswap' sensor, provides FRU M-state
record(mbbi, "$(dev):FRU0:MSTATE") {
  field(SCAN, "10 second")
#  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP,  "#B0 C240 N1 @$(link)+hs")
  field(ONVL, "0x01")
  field(ONST, "Not Installed")
  field(TWVL, "0x02")
  field(TWST, "Inactive")
  field(THVL, "0x04")
  field(THST, "Activation Request")
  field(FRVL, "0x08")
  field(FRST, "Activation In Progress")
  field(FVVL, "0x10")
  field(FVST, "Active")
  field(SXVL, "0x20")
  field(SXST, "Deactivation Request")
  field(SVVL, "0x40")
  field(SVST, "Deactivation In Progress")
  field(EIVL, "0x80")
  field(EIST, "Communication Lost")
  alias("$(dev):FRU0_MSTATE")
}

record(ai, "$(dev):FRU0:FRUID") {
  field(DESC, "Module type")
  field(VAL,  "0")
  field(PINI, "YES")
  alias("$(dev):FRU0_FRUID")
}

record(stringin, "$(dev):FRU0:BMANUF") {
  field(DESC, "Board manufacturer")
  field(SCAN, "I/O Intr")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+bmf")
  field(PINI, "YES")
  field(FLNK, "$(dev):FRU0:BPRODNAME")
  alias("$(dev):FRU0_BMANUF")
}

record(stringin, "$(dev):FRU0:BPRODNAME") {
  field(DESC, "Board product name")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+bp")
  field(FLNK, "$(dev):FRU0:BSN")
  alias("$(dev):FRU0_BPRODNAME")
}

record(stringin, "$(dev):FRU0:BSN") {
  field(DESC, "Board serial number")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+bsn")
  field(FLNK, "$(dev):FRU0:BPARTNUMBER")
  alias("$(dev):FRU0_BSN")
}

record(stringin, "$(dev):FRU0:BPARTNUMBER") {
  field(DESC, "Board part number")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+bpn")
  field(FLNK, "$(dev):FRU0:PMANUF")
  alias("$(dev):FRU0_BPARTNUMBER")
}

record(stringin, "$(dev):FRU0:PMANUF") {
  field(DESC, "Product manufacturer")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+pmf")
  field(FLNK, "$(dev):FRU0:PPRODNAME")
  alias("$(dev):FRU0_PMANUF")
}

record(stringin, "$(dev):FRU0:PPRODNAME") {
  field(DESC, "Product product name")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+pp")
  field(FLNK, "$(dev):FRU0:PSN")
  alias("$(dev):FRU0_PPRODNAME")
}

record(stringin, "$(dev):FRU0:PSN") {
  field(DESC, "Product serial number")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+psn")
  field(FLNK, "$(dev):FRU0:PPARTNUMBER")
  alias("$(dev):FRU0_PSN")
}

record(stringin, "$(dev):FRU0:PPARTNUMBER") {
  field(DESC, "Product part number")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+ppn")
  alias("$(dev):FRU0_PPARTNUMBER")
}

record(mbbo, "$(dev):FRU0:POWERCTL") {
  field(DESC, "Payload power control")
  field(DTYP, "MCHsensor")
  field(OMSL, "supervisory")
  field(OUT, "#B0 C0 N0  @$(link)+fru")
  field(ZRST, "Power off")
  field(ONST, "Power on")
#  reset not supported yet
#  field(TWST, "Hard reset")
  alias("$(dev):FRU0_POWERCTL")
}

record(ai, "$(dev):FRU0:PWR") {
  field(DESC, "Steady state power draw")
  field(SCAN, "10 second")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C0 N0  @$(link)+pwr")
  field(EGU, "W")
  field(FLNK, "$(dev):FRU0:PWRDES")
  alias("$(dev):FRU0_PWR")
}

record(ai, "$(dev):FRU0:PWRDES") {
  field(DESC, "Desired steady state power draw")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C1 N0  @$(link)+pwr")
  field(EGU, "W")
  field(FLNK, "$(dev):FRU0:EPWR")
  alias("$(dev):FRU0_PWRDES")
}

record(ai, "$(dev):FRU0:EPWR") {
  field(DESC, "Early power draw")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C2 N0  @$(link)+pwr")
  field(EGU, "W")
  field(FLNK, "$(dev):FRU0:EPWRDES")
  alias("$(dev):FRU0_EPWR")
}

record(ai, "$(dev):FRU0:EPWRDES") {
  field(DESC, "Desired early power draw")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C3 N0  @$(link)+pwr")
  field(EGU, "W")
  field(FLNK, "$(dev):FRU0:PWRDLY")
  alias("$(dev):FRU0_EPWRDES")
}

record(ai, "$(dev):FRU0:PWRDLY") {
  field(DESC, "FRU delay to stable power")
  field(DTYP, "FRUinfo")
  field(INP, "#B0 C4 N0  @$(link)+pwr")
  field(EGU, "s")
  field(FLNK, "$(dev):FRU0:PWRDYN")
  alias("$(dev):FRU0_PWRDLY")
}

record(mbbi, "$(dev):FRU0:PWRDYN") {
  field(DESC, "FRU supports dynamic pwr reconfig")
  field(DTYP, "MCHsensor")
  field(INP,  "#B0 C0 N0  @$(link)+pwr")
  field(ZRST, "No")
  field(ZRVL, "0")
  field(ONST, "Yes")
  field(ONVL, "1")
  alias("$(dev):FRU0_PWRDYN")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP1") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N1 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP1P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP1")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP1P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N1 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP1P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP2") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N2 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP2P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP2")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP2P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N2 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP2P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP3") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N3 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP3P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP3")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP3P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N3 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP3P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP4") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N4 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP4P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP4")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP4P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N4 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP4P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP5") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N5 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP5P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP5")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP5P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N5 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP5P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP6") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N6 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP6P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP6")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP6P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N6 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP6P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP7") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N7 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP7P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP7")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP7P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N7 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP7P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP8") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N8 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP8P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP8")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP8P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N8 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP8P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP9") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N9 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP9P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP9")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP9P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N9 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP9P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP10") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N10 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP10P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP10")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP10P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N10 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP10P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP11") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N11 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP11P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP11")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP11P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N11 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP11P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP12") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N12 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP12P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP12")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP12P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N12 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP12P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP13") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N13 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP13P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP13")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP13P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N13 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP13P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP14") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N14 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP14P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP14")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP14P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N14 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP14P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP15") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N15 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP15P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP15")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP15P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N15 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP15P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP16") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N16 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP16P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP16")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP16P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N16 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP16P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP17") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N17 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP17P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP17")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP17P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N17 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP17P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP18") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N18 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP18P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP18")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP18P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N18 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP18P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP19") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N19 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP19P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP19")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP19P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N19 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP19P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):TEMP20") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N20 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):TEMP20P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):TEMP20")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):TEMP20P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C1 N20 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):TEMP20P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V1") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N1 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V1P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V1")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V1P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N1 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V1P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V2") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N2 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V2P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V2")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V2P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N2 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V2P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V3") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N3 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V3P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V3")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V3P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N3 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V3P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V4") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N4 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V4P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V4")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V4P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N4 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V4P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V5") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N5 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V5P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V5")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V5P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N5 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V5P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V6") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N6 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V6P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V6")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V6P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N6 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V6P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V7") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N7 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V7P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V7")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V7P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N7 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V7P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V8") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N8 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V8P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V8")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V8P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N8 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V8P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V9") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N9 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V9P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V9")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V9P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N9 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V9P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V10") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N10 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V10P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V10")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V10P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N10 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V10P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V11") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N11 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V11P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V11")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V11P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N11 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V11P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V12") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N12 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V12P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V12")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V12P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N12 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V12P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V13") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N13 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V13P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V13")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V13P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N13 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V13P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V14") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N14 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V14P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V14")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V14P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N14 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V14P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V15") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N15 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V15P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V15")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V15P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N15 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V15P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V16") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N16 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V16P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V16")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V16P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N16 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V16P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V17") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N17 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V17P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V17")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V17P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N17 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V17P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V18") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N18 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V18P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V18")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V18P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N18 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V18P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V19") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N19 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V19P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V19")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V19P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N19 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V19P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):V20") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N20 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):V20P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):V20")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):V20P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C2 N20 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):V20P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I1") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N1 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I1P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I1")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I1P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N1 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I1P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I2") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N2 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I2P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I2")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I2P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N2 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I2P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I3") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N3 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I3P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I3")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I3P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N3 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I3P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I4") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N4 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I4P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I4")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I4P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N4 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I4P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I5") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N5 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I5P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I5")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I5P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N5 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I5P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I6") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N6 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I6P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I6")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I6P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N6 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I6P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I7") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N7 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I7P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I7")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I7P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N7 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I7P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I8") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N8 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I8P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I8")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I8P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N8 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I8P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I9") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N9 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I9P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I9")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I9P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N9 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I9P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I10") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N10 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I10P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I10")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I10P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N10 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I10P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I11") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N11 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I11P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I11")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I11P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N11 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I11P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I12") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N12 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I12P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I12")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I12P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N12 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I12P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I13") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N13 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I13P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I13")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I13P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N13 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I13P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I14") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N14 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I14P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I14")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I14P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N14 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I14P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I15") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N15 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I15P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I15")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I15P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N15 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I15P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I16") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N16 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I16P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I16")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I16P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N16 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I16P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I17") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N17 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I17P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I17")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I17P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N17 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I17P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I18") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N18 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I18P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I18")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I18P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N18 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I18P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I19") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N19 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I19P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I19")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I19P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N19 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I19P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):I20") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N20 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):I20P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):I20")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):I20P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C3 N20 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):I20P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED1") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N1 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED1P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED1")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED1P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N1 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED1P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED2") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N2 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED2P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED2")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED2P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N2 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED2P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED3") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N3 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED3P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED3")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED3P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N3 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED3P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED4") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N4 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED4P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED4")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED4P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N4 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED4P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED5") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N5 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED5P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED5")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED5P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N5 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED5P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED6") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N6 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED6P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED6")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED6P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N6 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED6P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED7") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N7 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED7P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED7")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED7P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N7 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED7P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED8") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N8 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED8P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED8")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED8P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N8 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED8P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED9") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N9 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED9P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED9")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED9P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N9 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED9P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED10") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N10 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED10P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED10")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED10P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N10 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED10P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED11") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N11 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED11P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED11")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED11P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N11 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED11P")
}
# ai sensor record
# DESC, EGU set by device support
record(ai, "$(dev):FANSPEED12") {
  field(DESC, "")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N12 @$(link)+sens")
  field(PREC, "1")
  field(SDIS, "$(dev):FANSPEED12P.SEVR")
  field(DISV, "2")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED12")
}

# sensor 'presence' record to indicate whether sensor 
# is active; used by edm visibility rules
record(bi, "$(dev):FANSPEED12P") {
  field(DESC, "Sensor presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B0 C4 N12 @$(link)+spres")
  field(ZNAM, "Not present")
  field(ZSV,  "MAJOR")
  field(ONAM, "Present")
  field(PINI, "YES")
#  alias("$(dev):FANSPEED12P")
}
record(aSub, "$(dev):TYPE_SUB") {
  field(DESC, "Determine LCLS device type")
  field(SNAM, "subMchTypeFacility")
  field(PINI, "YES")
  field(FTA,  "DOUBLE")
  field(INPA, "$(dev):TYPE_RAW MS CP")
# B-H are 'type' values to be used by
# facility-specific software (in particular
# LCLS IOCManager)
# B is type value for Unknown device
  field(FTB,  "DOUBLE")
  field(INPB, "25")
# C is type value for Vadatech MCH
  field(FTC,  "DOUBLE")
  field(INPC, "26")
# D is type value for NAT MCH
  field(FTD,  "DOUBLE")
  field(INPD, "27")
# E is type value for Supermicro server
  field(FTE,  "DOUBLE")
  field(INPE, "28")
# F is type value for Pentair shelf manager
  field(FTF,  "DOUBLE")
  field(INPF, "30")
# G is type value for Artesyn shelf manager
  field(FTG,  "DOUBLE")
  field(INPG, "30")
# H is type value for Advantech server
  field(FTH,  "DOUBLE")
  field(INPH, "28")
# VALA is 'type' value used in LCLS IOCManager
  field(FTVA, "DOUBLE")
  field(FLNK, "$(dev):TYPE_DESC")
}

# Copy description to TYPE.DESC
record(stringout, "$(dev):TYPE_DESC") {
  field(OMSL, "closed_loop")
  field(DOL,  "$(dev):TYPE_RAW")
  field(OUT,  "$(dev):TYPE.DESC")
  field(FLNK, "$(dev):TYPE")
}

# DESC filled in by TYPE_DESC PV
record(ai, "$(dev):TYPE") {
  field(DESC, "")
  field(INP, "$(dev):TYPE_SUB.VALA")
}
