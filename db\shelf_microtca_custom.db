#==============================================================================
#
# 自定义MicroTCA机箱数据库文件
# 基于实际传感器列表 microTCA_sensor_list.txt 创建
# 总计148个实际传感器，避免无效PV
#
# 使用方法:
# dbLoadRecords("db/shelf_microtca_custom.db", "dev=CRAT:MICROTCA:01,link=microtca-test,location=Lab-Rack1")
#
#==============================================================================

# 系统基础记录
record(mbbi, "$(dev):TYPE_RAW") {
 field(DESC, "Device type")
 field(DTYP, "MCHsensor")
 field(SCAN, "I/O Intr")
 field(INP, "#B0 C0 N0 @$(link)+mch")
 field(ZRST, "Unknown")
 field(ZRVL, "0")
 field(ZRSV, "MAJOR")
 field(ONST, "MicroTCA")
 field(ONVL, "1")
 field(ONSV, "NO_ALARM")
 field(TWST, "MicroTCA")
 field(TWVL, "2")
 field(TWSV, "NO_ALARM")
 field(THST, "Supermicro")
 field(THVL, "3")
 field(THSV, "NO_ALARM")
 field(FRST, "ATCA")
 field(FRVL, "4")
 field(FRSV, "NO_ALARM")
 field(FVVL, "5")
 field(FVST, "ATCA")
 field(FVSV, "NO_ALARM")
 field(SXVL, "6")
 field(SXST, "Advantech")
 field(SXSV, "NO_ALARM")
 field(PINI, "YES")
}

record(stringin, "$(dev):LOC") {
  field(PINI, "YES")
  field(VAL, "$(location)")
  info(autosaveFields,"VAL")
}

# 控制调试消息详细程度
record(mbbo, "$(dev):DBG") {
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(ZRVL, "0")
 field(ZRST, "Off")
 field(ONVL, "1")
 field(ONST, "Low")
 field(TWVL, "2")
 field(TWST, "Med")
 field(THVL, "3")
 field(THST, "High")
 field(OUT,  "#B0 C0 N0 @$(link)+dbg")
 field(VAL,  "0")
 field(PINI, "YES")
}

record(bi, "$(dev):ONLNSTAT") {
 field(DESC, "MCH online status")
 field(DTYP, "MCHsensor")
 field(SCAN, "I/O Intr")
 field(INP, "#B0 C0 N0 @$(link)+stat")
 field(ZNAM, "Offline")
 field(ZSV, "MAJOR")
 field(ONAM, "Online")
 field(OSV, "NO_ALARM")
 field(PINI, "YES")
 alias("$(dev):STAT")
}

record(mbbi, "$(dev):INIT") {
 field(DESC, "MCH comm initialized")
 field(DTYP, "MCHsensor")
 field(SCAN, "I/O Intr")
 field(INP, "#B0 C0 N0 @$(link)+init")
 field(ZRST, "Not initialized")
 field(ZRVL, "0")
 field(ZRSV, "MAJOR")
 field(ONST, "Initializing...")
 field(ONVL, "1")
 field(ONSV, "MINOR")
 field(TWST, "Initialized")
 field(TWVL, "2")
 field(TWSV, "NO_ALARM")
 field(THST, "Initialize failed")
 field(THVL, "3")
 field(THSV, "MAJOR")
 field(PINI, "YES")
}

record(bo, "$(dev):CONNECT") {
 field(DESC, "Enable comm with MCH")
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(OUT,  "#B0 C0 N0 @$(link)+sess")
 field(ZNAM, "Disconnect")
 field(ZSV,  "MAJOR")
 field(ONAM, "Connect")
 field(OSV,  "NO_ALARM")
 field(VAL, "1")
 field(RVAL, "1")
 field(PINI, "YES") 
 info(autosaveFields,"VAL RVAL")
}

# 控制传感器扫描周期 [秒]
record(mbbo, "$(dev):SENSOR_SCAN_PERIOD") {
 field(DTYP, "MCHsensor")
 field(OMSL, "supervisory")
 field(ZRVL, "0")
 field(ZRST, "5 seconds")
 field(ONVL, "1")
 field(ONST, "10 seconds")
 field(TWVL, "2")
 field(TWST, "20 seconds")
 field(THVL, "3")
 field(THST, "30 seconds")
 field(FRVL, "4")
 field(FRST, "60 seconds")
 field(OUT,  "#B0 C0 N0 @$(link)+scan")
 field(VAL,  "1")
 field(RVAL, "1")
 field(PINI, "YES")
 info(autosaveFields,"VAL RVAL")
}

#==============================================================================
# AMC1 传感器 (基于实际传感器列表第2-27行)
# FRU ID: 5 (B5)
#==============================================================================

# AMC1 存在状态
record(bi, "$(dev):AMC1:P") {
  field(DESC, "AMC1 presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C0 N0 @$(link)+fpres")
  field(ZNAM, "Not present")
  field(ONAM, "Present")
  field(PINI, "YES")
}

# AMC1 热插拔状态
record(mbbi, "$(dev):AMC1:HOTSWAP") {
  field(DESC, "AMC1 hotswap status")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C0 N1 @$(link)+hs")
  field(PINI, "YES")
}

# AMC1 电压传感器
record(ai, "$(dev):AMC1:V_12V") {
  field(DESC, "AMC +12V")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N1 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_FMC0_VADJ") {
  field(DESC, "FMC0 VADJ")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N2 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_FMC1_VADJ") {
  field(DESC, "FMC1 VADJ")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N3 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_FPGA_1V8") {
  field(DESC, "FPGA +1.8V")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N4 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_FPGA_0V85") {
  field(DESC, "FPGA +0.85V")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N5 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_FPGA_0V9VA") {
  field(DESC, "FPGA +0.9VA")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N6 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_FPGA_1V2VA") {
  field(DESC, "FPGA +1.2VA")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N7 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_DDR_1V2VT") {
  field(DESC, "DDR +1.2VT")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N8 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_DDR_1V2VB") {
  field(DESC, "DDR +1.2VB")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N9 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:V_IO_3V3") {
  field(DESC, "IO +3.3V")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C2 N10 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

# AMC1 电流传感器
record(ai, "$(dev):AMC1:I_RTM_12V") {
  field(DESC, "RTM +12V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N1 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FMC0_12V") {
  field(DESC, "FMC0 +12V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N2 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FMC0_VADJ") {
  field(DESC, "FMC0 VADJ Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N3 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FMC0_3V3") {
  field(DESC, "FMC0 +3.3V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N4 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FMC1_12V") {
  field(DESC, "FMC1 +12V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N5 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FMC1_VADJ") {
  field(DESC, "FMC1 VADJ Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N6 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FMC1_3V3") {
  field(DESC, "FMC1 +3.3V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N7 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FPGA_0V85") {
  field(DESC, "FPGA +0.85V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N8 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FPGA_1V8") {
  field(DESC, "FPGA +1.8V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N9 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FPGA_0V9VA") {
  field(DESC, "FPGA +0.9VA Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N10 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_FPGA_1V2VA") {
  field(DESC, "FPGA +1.2VA Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N11 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_DDR_1V2VT") {
  field(DESC, "DDR +1.2VT Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N12 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_DDR_1V2VB") {
  field(DESC, "DDR +1.2VB Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N13 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

record(ai, "$(dev):AMC1:I_IO_3V3") {
  field(DESC, "IO +3.3V Current")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C3 N14 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "A")
  field(PINI, "YES")
}

# AMC1 温度传感器
record(ai, "$(dev):AMC1:TEMP_UCD") {
  field(DESC, "TEMP UCD")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B5 C1 N1 @$(link)+sens")
  field(PREC, "1")
  field(EGU, "C")
  field(PINI, "YES")
}

#==============================================================================
# 冷却单元1 (CU1) 传感器 (基于实际传感器列表第28-57行)
# FRU ID: 40 (B40)
#==============================================================================

# CU1 存在状态
record(bi, "$(dev):CU1:P") {
  field(DESC, "CU1 presence")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B40 C0 N0 @$(link)+fpres")
  field(ZNAM, "Not present")
  field(ONAM, "Present")
  field(PINI, "YES")
}

# CU1 热插拔状态
record(mbbi, "$(dev):CU1:HOTSWAP") {
  field(DESC, "CU1 hotswap status")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B40 C0 N1 @$(link)+hs")
  field(PINI, "YES")
}

# CU1 电压传感器
record(ai, "$(dev):CU1:V_3V3") {
  field(DESC, "+3.3V")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B40 C2 N1 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):CU1:V_12V") {
  field(DESC, "+12V")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B40 C2 N2 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}

record(ai, "$(dev):CU1:V_12V_1") {
  field(DESC, "+12V_1")
  field(SCAN, "I/O Intr")
  field(DTYP, "MCHsensor")
  field(INP, "#B40 C2 N3 @$(link)+sens")
  field(PREC, "3")
  field(EGU, "V")
  field(PINI, "YES")
}
