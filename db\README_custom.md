# 自定义MicroTCA数据库文件说明

## 概述

`shelf_microtca_custom.db` 是根据实际MicroTCA机箱传感器列表创建的精简数据库文件，只包含实际存在的148个传感器，避免了原始数据库文件中的7457个PV中的大量无效PV。

## 文件对比

| 文件 | PV数量 | 说明 |
|------|--------|------|
| `shelf_microtca_12slot.db` | 7457 | 原始完整数据库，包含大量无效PV |
| `shelf_microtca_custom.db` | ~200 | 精简数据库，只包含实际传感器 |

## 传感器映射

基于 `microTCA_sensor_list.txt` 文件中的实际传感器列表，数据库包含以下组件：

### 1. 系统控制记录
- `$(dev):ONLNSTAT` - MCH在线状态
- `$(dev):INIT` - 初始化状态
- `$(dev):CONNECT` - 连接控制
- `$(dev):DBG` - 调试级别控制
- `$(dev):SENSOR_SCAN_PERIOD` - 传感器扫描周期

### 2. AMC1 模块 (FRU ID: 5)
**存在状态:**
- `$(dev):AMC1:P` - 模块存在状态
- `$(dev):AMC1:HOTSWAP` - 热插拔状态

**电压传感器 (10个):**
- `$(dev):AMC1:V_12V` - +12V电压
- `$(dev):AMC1:V_FMC0_VADJ` - FMC0 VADJ电压
- `$(dev):AMC1:V_FMC1_VADJ` - FMC1 VADJ电压
- `$(dev):AMC1:V_FPGA_1V8` - FPGA +1.8V电压
- `$(dev):AMC1:V_FPGA_0V85` - FPGA +0.85V电压
- `$(dev):AMC1:V_FPGA_0V9VA` - FPGA +0.9VA电压
- `$(dev):AMC1:V_FPGA_1V2VA` - FPGA +1.2VA电压
- `$(dev):AMC1:V_DDR_1V2VT` - DDR +1.2VT电压
- `$(dev):AMC1:V_DDR_1V2VB` - DDR +1.2VB电压
- `$(dev):AMC1:V_IO_3V3` - IO +3.3V电压

**电流传感器 (14个):**
- `$(dev):AMC1:I_RTM_12V` - RTM +12V电流
- `$(dev):AMC1:I_FMC0_12V` - FMC0 +12V电流
- `$(dev):AMC1:I_FMC0_VADJ` - FMC0 VADJ电流
- `$(dev):AMC1:I_FMC0_3V3` - FMC0 +3.3V电流
- `$(dev):AMC1:I_FMC1_12V` - FMC1 +12V电流
- `$(dev):AMC1:I_FMC1_VADJ` - FMC1 VADJ电流
- `$(dev):AMC1:I_FMC1_3V3` - FMC1 +3.3V电流
- `$(dev):AMC1:I_FPGA_0V85` - FPGA +0.85V电流
- `$(dev):AMC1:I_FPGA_1V8` - FPGA +1.8V电流
- `$(dev):AMC1:I_FPGA_0V9VA` - FPGA +0.9VA电流
- `$(dev):AMC1:I_FPGA_1V2VA` - FPGA +1.2VA电流
- `$(dev):AMC1:I_DDR_1V2VT` - DDR +1.2VT电流
- `$(dev):AMC1:I_DDR_1V2VB` - DDR +1.2VB电流
- `$(dev):AMC1:I_IO_3V3` - IO +3.3V电流

**温度传感器 (1个):**
- `$(dev):AMC1:TEMP_UCD` - UCD温度

### 3. 冷却单元1 (CU1) - FRU ID: 40
**基本状态:**
- `$(dev):CU1:P` - 存在状态
- `$(dev):CU1:HOTSWAP` - 热插拔状态
- `$(dev):CU1:AIR_FILTER` - 空气过滤器状态

**电压传感器 (3个):**
- `$(dev):CU1:V_3V3` - +3.3V电压
- `$(dev):CU1:V_12V` - +12V电压
- `$(dev):CU1:V_12V_1` - +12V_1电压

**温度传感器 (2个):**
- `$(dev):CU1:TEMP_LM75_1` - LM75温度传感器1
- `$(dev):CU1:TEMP_LM75_2` - LM75温度传感器2

**风扇传感器 (6个):**
- `$(dev):CU1:FAN1-6` - 风扇1-6转速

### 4. 冷却单元2 (CU2) - FRU ID: 41
与CU1相同的传感器配置，PV名称为 `$(dev):CU2:*`

### 5. 电源模块2 (PM2) - FRU ID: 51
**基本状态:**
- `$(dev):PM2:P` - 存在状态
- `$(dev):PM2:HOTSWAP` - 热插拔状态

**温度传感器 (4个):**
- `$(dev):PM2:TEMP_DCDC_UPD` - DCDC UPD温度
- `$(dev):PM2:TEMP_PATH_UPD` - PATH UPD温度
- `$(dev):PM2:TEMP_COOLER_UPM` - COOLER UPM温度
- `$(dev):PM2:TEMP_TRAFO_UPM` - TRAFO UPM温度

**电压传感器 (4个):**
- `$(dev):PM2:V_MP` - MP电压
- `$(dev):PM2:V_SMP` - SMP电压
- `$(dev):PM2:V_12V` - +12V电压
- `$(dev):PM2:V_3V3` - +3.3V电压

**电流传感器 (4个):**
- `$(dev):PM2:I_MP` - MP电流
- `$(dev):PM2:I_SMP` - SMP电流
- `$(dev):PM2:I_12V` - +12V电流
- `$(dev):PM2:I_3V3` - +3.3V电流

**风扇传感器 (2个):**
- `$(dev):PM2:FAN1-2` - 风扇1-2转速

### 6. MCH控制器 (FRU ID: 0)
**温度传感器 (3个):**
- `$(dev):MCH:TEMP_FPGA` - FPGA温度
- `$(dev):MCH:TEMP_BOARD` - 板卡温度
- `$(dev):MCH:TEMP_INLET` - 进气温度

**电压传感器 (6个):**
- `$(dev):MCH:V_3V3` - +3.3V电压
- `$(dev):MCH:V_12V` - +12V电压
- `$(dev):MCH:V_5V` - +5V电压
- `$(dev):MCH:V_1V8` - +1.8V电压
- `$(dev):MCH:V_1V2` - +1.2V电压
- `$(dev):MCH:V_1V0` - +1.0V电压

**电流传感器 (3个):**
- `$(dev):MCH:I_3V3` - +3.3V电流
- `$(dev):MCH:I_12V` - +12V电流
- `$(dev):MCH:I_5V` - +5V电流

### 7. FRU信息记录
每个模块包含制造商、产品、部件号、序列号等信息：
- `$(dev):AMC1:FRU_*`
- `$(dev):CU1:FRU_*`
- `$(dev):CU2:FRU_*`
- `$(dev):PM2:FRU_*`
- `$(dev):MCH:FRU_*`

## 使用方法

### 1. 修改启动脚本
将 `st1.cmd` 中的数据库加载行修改为：
```bash
dbLoadRecords("db/shelf_microtca_custom.db", "dev=CRAT:MICROTCA:01,link=microtca-test,location=Lab-Rack1")
```

### 2. 使用提供的启动脚本
直接使用 `st_custom.cmd`：
```bash
cd iocBoot/sioc-ipmicomm-test
./st_custom.cmd
```

### 3. 验证传感器
启动后使用以下命令验证：
```bash
# 列出所有PV
dbl

# 检查系统状态
caget CRAT:MICROTCA:01:ONLNSTAT
caget CRAT:MICROTCA:01:INIT

# 监控关键传感器
camonitor CRAT:MICROTCA:01:AMC1:TEMP_UCD
camonitor CRAT:MICROTCA:01:CU1:FAN1
camonitor CRAT:MICROTCA:01:PM2:TEMP_DCDC_UPD
```

## 优势

1. **精简高效**: 只包含实际存在的传感器，避免无效PV
2. **快速启动**: 减少初始化时间和内存占用
3. **易于维护**: 清晰的传感器映射关系
4. **实用性强**: 基于真实硬件配置创建

## 注意事项

1. 此数据库基于您提供的 `microTCA_sensor_list.txt` 创建
2. 如果硬件配置发生变化，需要相应更新数据库文件
3. FRU ID和传感器编号必须与实际硬件匹配
4. 建议在生产环境使用前进行充分测试
