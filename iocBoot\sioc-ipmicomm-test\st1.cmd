#!../../bin/linux-x86_64/ipmiCommIoc
#==============================================================
#
#  示例启动脚本 - 请根据您的实际设备修改
#
#  使用方法:
#  1. 复制此文件为 st_local.cmd
#  2. 修改设备 IP 地址和配置
#  3. 运行: ../../bin/linux-x86_64/ipmiCommIoc st_local.cmd
#
#==============================================================

# 设置环境变量
< envPaths
epicsEnvSet("EPICS_CA_SERVER_PORT", "10022")
# 切换到应用顶层目录
cd ../../

# 加载数据库定义
dbLoadDatabase("dbd/ipmiCommIoc.dbd")
ipmiCommIoc_registerRecordDeviceDriver(pdbbase)

# 可选：加载 IOC 管理数据库（如果有 iocAdmin）
# dbLoadRecords("db/iocAdminSoft.db","IOC=IPMITEST")

#==============================================================
# 设备配置示例
# 请根据您的实际设备修改 IP 地址
#==============================================================

# 示例1: MicroTCA 机箱监控
# 替换 "*************" 为您的 MCH IP 地址
drvAsynIPPortConfigure("microtca-test", "************:623 udp", 0, 0, 0)
mchInit("microtca-test")
dbLoadRecords("db/shelf_microtca_12slot.db", "dev=CRAT:MICROTCA:01,link=microtca-test,location=Lab-Rack1")

# 示例2: ATCA 机箱监控
# 替换 "*************" 为您的 ATCA Shelf Manager IP 地址
# drvAsynIPPortConfigure("atca-test", "*************:623 udp", 0, 0, 0)
# mchInit("atca-test")
# dbLoadRecords("db/shelf_atca_7slot.db", "dev=CRAT:ATCA:01,link=atca-test,location=Lab-Rack2")

# 示例3: 服务器监控 (Supermicro/Advantech)
# 替换 "*************" 为您的服务器 BMC IP 地址
# drvAsynIPPortConfigure("server-test", "*************:623 udp", 0, 0, 0)
# mchInit("server-test")
# dbLoadRecords("db/server_pc.db", "dev=SERVER:01,link=server-test,location=Lab-Rack3")

#==============================================================
# 调试设置（可选）
#==============================================================
# 启用 asyn 调试（取消注释以启用）
# asynSetTraceMask("microtca-test", -1, 0x9)
# asynSetTraceIOMask("microtca-test", -1, 0x5)

# 切换回 IOC 启动目录
cd ${TOP}/iocBoot/${IOC}

# 初始化 IOC
iocInit()

#==============================================================
# IOC 启动后的配置（可选）
#==============================================================
# 设置传感器扫描周期（秒）
# epicsThreadSleep(2)
# dbpf("CRAT:MICROTCA:01:SCAN_PERIOD", "10")

# 设置调试级别
# dbpf("CRAT:MICROTCA:01:DBG", "1")

# 显示一些状态信息
echo "IOC 启动完成!"
echo "可用的 PV 前缀:"
echo "  CRAT:MICROTCA:01:*  - MicroTCA 机箱状态"
echo ""
echo "常用命令:"
echo "  dbl                 - 列出所有 PV"
echo "  caget CRAT:MICROTCA:01:CONNECT - 检查连接状态"
echo "  caget CRAT:MICROTCA:01:INIT    - 检查初始化状态"
echo ""

# End of file
