ipmiMsg.o: ../ipmiMsg.c /usr/local/EPICS/base-7.0.6/include/errlog.h \
 /usr/local/EPICS/base-7.0.6/include/libComAPI.h \
 /usr/local/EPICS/base-7.0.6/include/compilerDependencies.h \
 /usr/local/EPICS/base-7.0.6/include/compiler/gcc/compilerSpecific.h \
 /usr/local/EPICS/base-7.0.6/include/epicsMutex.h \
 /usr/local/EPICS/base-7.0.6/include/epicsAssert.h \
 /usr/local/EPICS/base-7.0.6/include/os/Linux/osdMutex.h \
 /home/<USER>/mTCA/asyn/include/asynDriver.h \
 /usr/local/EPICS/base-7.0.6/include/epicsStdio.h \
 /usr/local/EPICS/base-7.0.6/include/epicsTempFile.h \
 /usr/local/EPICS/base-7.0.6/include/epicsTime.h \
 /usr/local/EPICS/base-7.0.6/include/epicsTypes.h \
 /usr/local/EPICS/base-7.0.6/include/os/Linux/osdTime.h \
 /usr/local/EPICS/base-7.0.6/include/errMdef.h \
 /usr/local/EPICS/base-7.0.6/include/ellLib.h \
 /usr/local/EPICS/base-7.0.6/include/epicsVersion.h \
 /home/<USER>/mTCA/asyn/include/asynAPI.h \
 /home/<USER>/mTCA/asyn/include/asynOctetSyncIO.h \
 /home/<USER>/mTCA/asyn/include/asynDriver.h \
 /usr/local/EPICS/base-7.0.6/include/iocsh.h \
 /usr/local/EPICS/base-7.0.6/include/epicsExport.h \
 /usr/local/EPICS/base-7.0.6/include/shareLib.h ../ipmiMsg.h ../drvMch.h \
 /usr/local/EPICS/base-7.0.6/include/epicsThread.h \
 /usr/local/EPICS/base-7.0.6/include/os/Linux/osdThread.h \
 /usr/local/EPICS/base-7.0.6/include/libComAPI.h \
 /usr/local/EPICS/base-7.0.6/include/epicsEvent.h \
 /usr/local/EPICS/base-7.0.6/include/os/Linux/osdEvent.h ../devMch.h \
 /usr/local/EPICS/base-7.0.6/include/dbCommon.h \
 /usr/local/EPICS/base-7.0.6/include/link.h \
 /usr/local/EPICS/base-7.0.6/include/dbDefs.h \
 /usr/local/EPICS/base-7.0.6/include/ellLib.h \
 /usr/local/EPICS/base-7.0.6/include/dbCoreAPI.h \
 /usr/local/EPICS/base-7.0.6/include/epicsMutex.h \
 /usr/local/EPICS/base-7.0.6/include/devSup.h \
 /usr/local/EPICS/base-7.0.6/include/epicsTime.h \
 /usr/local/EPICS/base-7.0.6/include/dbScan.h \
 /usr/local/EPICS/base-7.0.6/include/menuScan.h \
 /usr/local/EPICS/base-7.0.6/include/epicsTypes.h ../ipmiDef.h \
 ../picmgDef.h
